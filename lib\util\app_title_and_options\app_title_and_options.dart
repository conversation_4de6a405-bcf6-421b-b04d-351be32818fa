import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';


class AppTitleAndOptions extends StatefulWidget {
  final String title;
  final double titlePaddingHorizontal;
  final Widget titleOption;
  final Widget? option;
  const AppTitleAndOptions({super.key, required this.title,  this.titleOption = const SizedBox(),this.option,  this.titlePaddingHorizontal = 0.0});

  @override
  State<AppTitleAndOptions> createState() => _AppTitleAndOptionsState();
}

class _AppTitleAndOptionsState extends State<AppTitleAndOptions> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        ///Title and title option
        Padding(
          padding:  EdgeInsets.symmetric(horizontal: widget.titlePaddingHorizontal),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(child: Text(widget.title,style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),)),
              widget.titleOption
            ],
          ),
        ),
        const SizedBox(height: 10), // Added spacing
        //Option
        Visibility(
            visible: widget.option != null,
            child: Row(
              children: [
                Expanded(child:widget.option!=null? widget.option!:const SizedBox(height:0))
              ],
            ))
      ],

    );
  }
}
