import 'dart:convert';
// import 'package:http/http.dart' as http;
import 'package:swadesic/env.dart';
import 'package:swadesic/model/base_url_response/base_url_response.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';

class BaseUrlService {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  BaseUrlService() {
    httpService = HttpService();
  }

  // endregion

// region Get base url
  Future<BaseUrlResponse> getBaseUrl() async {
    // get body [for POST request]
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.getApiWithoutToken(AppConstants.getBaseUrl);

    //Decrypting
    Map<String, dynamic> data = json.decode(CommonMethods.decodeBase32(response['6L3FGJSS2NZXFUYTONJ']));

    //print(data);
    return BaseUrlResponse.fromJson(data); // Assuming you have a class to deserialize the response
  }
// endregion

// //region get base url from web app 
// Future<BaseUrlResponse> getBaseUrlFromWebApp() async {
//   final url = Uri.parse('https://baseurl.swadesic.com/baseurl.json');

//   try {
//     final response = await http.get(url);

//     if (response.statusCode == 200) {
//       final data = jsonDecode(response.body);
//       return BaseUrlResponse.fromJson(data);
//     } else {
//       throw Exception('Failed to load data: ${response.statusCode}');
//     }
//   } catch (e) {
//     throw Exception('Failed to load data: $e');
//   }
// }

// //endregion

}