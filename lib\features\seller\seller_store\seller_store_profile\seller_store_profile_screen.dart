import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/common_buyer_seller_screen/store_handle_and_user_name/store_handle_and_user_name.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_profile/seller_store_profile_bloc.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

// region Seller Store Profile Screen
class SellerStoreProfileScreen extends StatefulWidget {
  final String storeReference;

  const SellerStoreProfileScreen({Key? key, required this.storeReference})
      : super(key: key);

  @override
  _SellerStoreProfileScreenState createState() =>
      _SellerStoreProfileScreenState();
}
// endregion

class _SellerStoreProfileScreenState extends State<SellerStoreProfileScreen> {
  // region Bloc
  late SellerStoreProfileBloc sellerStoreProfileBloc;

  // endregion

  // region Init
  @override
  void initState() {
    sellerStoreProfileBloc =
        SellerStoreProfileBloc(context, widget.storeReference);
    sellerStoreProfileBloc.init();
    super.initState();
  }

  // endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        CommonMethods.closeKeyboard(context);
      },
      child: Scaffold(
        backgroundColor: AppColors.appWhite,
        appBar: appBar(),
        body: SafeArea(child: body()),
      ),
    );
  }

  // endregion

  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
        context: context,
        isCustomTitle: false,
        title: AppStrings.storeProfile,
        onTapLeading: () {
          sellerStoreProfileBloc.onTapLeading();
        },
        isDefaultMenuVisible: false,
        isMembershipVisible: false,
        isCartVisible: false,
        isTextButtonVisible: true,
        textButtonWidget: StreamBuilder<SellerStoreProfileState>(
            stream: sellerStoreProfileBloc.storeProfileCtrl.stream,
            builder: (context, snapshot) {
              if (snapshot.data == SellerStoreProfileState.Success) {
                return AppCommonWidgets.appBarTextButtonText(
                    text: AppStrings.save);
              }
              return const SizedBox();
            }),
        onTapTextButton: () async {
          // sellerStoreProfileBloc.addEditStoreLink();
          await sellerStoreProfileBloc.editStoreDetail();
        });
  }

  //endregion

  // region Body
  Widget body() {
    return StreamBuilder<SellerStoreProfileState>(
        stream: sellerStoreProfileBloc.storeProfileCtrl.stream,
        initialData: SellerStoreProfileState.Loading,
        builder: (context, snapshot) {
          if (snapshot.data == SellerStoreProfileState.Success) {
            return SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Store header with cover image and profile (no horizontal padding)
                  storeProfileHeader(),
                  // Edit Store Logo button (outside header, with padding)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: InkWell(
                        onTap: () {
                          CommonMethods.closeKeyboard(context);
                          sellerStoreProfileBloc.goToAddImageScreen();
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Text(
                            "edit store logo",
                            style: AppTextStyle.contentText0(
                              textColor: AppColors.brandBlack,
                            ).copyWith(
                              decoration: TextDecoration.underline,
                              decorationColor: AppColors.brandBlack,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Content with padding
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        verticalSizedBox(10),
                        editFormSections(),
                        AppCommonWidgets.bottomListSpace(context: context),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }
          if (snapshot.data == SellerStoreProfileState.Failed) {
            return Center(
              child: AppCommonWidgets.errorWidget(onTap: () {
                sellerStoreProfileBloc.init();
              }),
            );
          }
          if (snapshot.data == SellerStoreProfileState.Loading) {
            return AppCommonWidgets.appCircularProgress();
          }
          return const SizedBox();
        });
  }

// endregion

  //region Store Profile Header (Cover + Logo)
  Widget storeProfileHeader() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // Cover image without padding - full width
          Stack(
            clipBehavior: Clip.none,
            children: [
              // Cover image from your method
              storeCoverImage(),
              // Profile + details overlapping the cover image with padding
              Positioned(
                bottom: -55,
                left: 10,
                right: 10,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // Store profile image only
                    storeImage(),
                    const SizedBox(width: 16),
                    // // Store name and handle
                    // Expanded(
                    //   child: Column(
                    //     crossAxisAlignment: CrossAxisAlignment.start,
                    //     children: [
                    //       const SizedBox(height: 20),
                    //       Row(
                    //         children: [
                    //           Expanded(
                    //             child: Text(
                    //               sellerStoreProfileBloc.singleStoreInfoResponse
                    //                       .data?.storeName ??
                    //                   "",
                    //               style: AppTextStyle.usernameHeading(
                    //                 textColor: AppColors.appBlack,
                    //               ).copyWith(
                    //                 fontWeight: FontWeight.bold,
                    //                 fontSize: 18,
                    //                 height: 1.2,
                    //               ),
                    //               overflow: TextOverflow.ellipsis,
                    //               maxLines: 1,
                    //             ),
                    //           ),
                    //           Visibility(
                    //             visible: sellerStoreProfileBloc
                    //                     .singleStoreInfoResponse
                    //                     .data
                    //                     ?.subscriptionType ==
                    //                 SubscriptionTypeEnum.PREMIUM.name,
                    //             child: VerifiedBadge(
                    //               margin: const EdgeInsets.only(left: 6),
                    //               height: 18,
                    //               width: 18,
                    //               subscriptionType:
                    //                   SubscriptionTypeEnum.PREMIUM.name,
                    //             ),
                    //           ),
                    //         ],
                    //       ),
                    //       Text(
                    //         "@${sellerStoreProfileBloc.singleStoreInfoResponse.data?.storehandle ?? ""}",
                    //         style: AppTextStyle.contentHeading0(
                    //           textColor: AppColors.writingBlack1,
                    //         ).copyWith(fontSize: 14),
                    //         overflow: TextOverflow.ellipsis,
                    //       ),
                    //     ],
                    //   ),
                    // ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 50), // For profile image overlap
        ],
      ),
    );
  }

  //region Edit Form Sections
  Widget editFormSections() {
    return Column(
      children: [
        AppTitleAndOptions(
          title: AppStrings.storeNameText,
          option: AppTextFields.allTextField(
              maxEntry: 30,
              context: context,
              textEditingController: sellerStoreProfileBloc.storeNameTextCtrl,
              hintText: AppStrings.storeNameText,
              onChanged: () {
                sellerStoreProfileBloc.isChangedSomething = true;
              }),
        ),
        verticalSizedBox(30),
        storeHandelAvailable(),
        verticalSizedBox(30),
        chooseBusinessCategory(),
        verticalSizedBox(30),
        manufacture(),
        verticalSizedBox(30),
        AppTitleAndOptions(
          title: AppStrings.aboutYourBusiness,
          option: AppTextFields.allTextField(
              textInputAction: TextInputAction.newline,
              keyboardType: TextInputType.multiline,
              context: context,
              maxEntry: 500,
              minLines: 5,
              maxLines: 5,
              textEditingController: sellerStoreProfileBloc.storeDesc,
              hintText: AppStrings.howCool,
              onChanged: () {
                sellerStoreProfileBloc.isChangedSomething = true;
              }),
        ),
        verticalSizedBox(30),
        websiteLink(),
      ],
    );
  }
  //endregion

  //region Store Cover Image
  Widget storeCoverImage() {
    if (sellerStoreProfileBloc.isCoverImageSelected) {
      return GestureDetector(
        onTap: () {
          sellerStoreProfileBloc.goToAddCoverImageScreen();
        },
        child: SizedBox(
          width: double.infinity,
          height: 150,
          child: Stack(
            children: [
              // Cover image - stretched completely horizontally without border radius
              SizedBox(
                width: double.infinity,
                height: 150,
                child:
                    kIsWeb && sellerStoreProfileBloc.webCoverImageBytes != null
                        ? Image.memory(
                            sellerStoreProfileBloc.webCoverImageBytes!,
                            fit: BoxFit.cover,
                            // cacheHeight: 300,
                            // cacheWidth: 600,
                          )
                        : Image.file(
                            File(sellerStoreProfileBloc.coverImageFile!.path),
                            fit: BoxFit.cover,
                            // cacheHeight: 300,
                            // cacheWidth: 600,
                          ),
              ),
              // Edit icon
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.edit,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return GestureDetector(
      onTap: () {
        sellerStoreProfileBloc.goToAddCoverImageScreen();
      },
      child: SizedBox(
        width: double.infinity,
        height: 150,
        child: Stack(
          children: [
            // Cover image or placeholder - stretched completely horizontally
            SizedBox(
              width: double.infinity,
              height: 150,
              child: sellerStoreProfileBloc
                              .singleStoreInfoResponse.data!.coverImage ==
                          null ||
                      sellerStoreProfileBloc
                          .singleStoreInfoResponse.data!.coverImage!.isEmpty
                  ? Container(
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [AppColors.borderColor1, AppColors.appWhite],
                        ),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.store,
                          size: 50,
                          color: AppColors.appBlack,
                        ),
                      ),
                    )
                  : extendedImage(
                      sellerStoreProfileBloc
                          .singleStoreInfoResponse.data!.coverImage!,
                      customPlaceHolder: AppImages.bannerPlaceHolder,
                      context,
                      400,
                      150,
                      fit: BoxFit.cover,
                    ),
            ),
            // Edit icon
            Positioned(
              top: 8,
              right: 8,
              child: InkWell(
                onTap: () {
                  print("Cover edit icon tapped!"); // Debug print
                  sellerStoreProfileBloc.goToAddCoverImageScreen();
                },
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.edit,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  //endregion

  //region Store Image (Logo)
  Widget storeImage() {
    return GestureDetector(
      onTap: () {
        print("Store logo tapped!");
        sellerStoreProfileBloc.goToAddImageScreen();
      },
      child: Container(
        height: 80,
        width: 80,
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.appWhite, width: 3),
          borderRadius: BorderRadius.circular(34),
        ),
        child: Stack(
          children: [
            // Make image tappable
            CustomImageContainer(
              width: 74,
              height: 74,
              imageUrl:
                  sellerStoreProfileBloc.singleStoreInfoResponse.data!.icon,
              imageType: CustomImageContainerType.store,
              showLevelBadge: true,
              level: sellerStoreProfileBloc
                  .singleStoreInfoResponse.data!.storeLevel,
              badgeWidth: 25,
              badgeHeight: 25,
            ),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Add logo
  Widget editLogo() {
    if (sellerStoreProfileBloc.isImageSelected) {
      return InkWell(
        onTap: () {
          sellerStoreProfileBloc.goToAddImageScreen();
        },
        child: Column(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(CommonMethods()
                  .getBorderRadius(
                      height: 100,
                      imageType: CustomImageContainerType
                          .store)), //region Get border radius
              child: SizedBox(
                height: 100,
                width: 100,
                child: kIsWeb && sellerStoreProfileBloc.webImageBytes != null
                    ? Image.memory(
                        sellerStoreProfileBloc.webImageBytes!,
                        fit: BoxFit.cover,
                        cacheHeight: 300,
                        cacheWidth: 300,
                      )
                    : Image.file(
                        File(sellerStoreProfileBloc.files!.path),
                        fit: BoxFit.cover,
                        cacheHeight: 300,
                        cacheWidth: 300,
                      ),
              ),
            ),
            editStoreLogo(),
          ],
        ),
      );
    }
    return InkWell(
      onTap: () {
        sellerStoreProfileBloc.goToAddImageScreen();
      },
      child: Column(
        children: [
          CustomImageContainer(
            width: 100,
            height: 100,
            imageUrl: sellerStoreProfileBloc.singleStoreInfoResponse.data!.icon,
            imageType: CustomImageContainerType.store,
          ),
          editStoreLogo(),
        ],
      ),
    );
  }

  //endregion

  //region Edit Cover Image
  Widget editCoverImage() {
    if (sellerStoreProfileBloc.isCoverImageSelected) {
      return InkWell(
        onTap: () {
          sellerStoreProfileBloc.goToAddCoverImageScreen();
        },
        child: Column(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                height: 120,
                width: double.infinity,
                child:
                    kIsWeb && sellerStoreProfileBloc.webCoverImageBytes != null
                        ? Image.memory(
                            sellerStoreProfileBloc.webCoverImageBytes!,
                            fit: BoxFit.cover,
                            cacheHeight: 300,
                            cacheWidth: 600,
                          )
                        : Image.file(
                            File(sellerStoreProfileBloc.coverImageFile!.path),
                            fit: BoxFit.cover,
                            cacheHeight: 300,
                            cacheWidth: 600,
                          ),
              ),
            ),
            verticalSizedBox(8),
            editStoreCoverImage(),
          ],
        ),
      );
    }
    return InkWell(
      onTap: () {
        sellerStoreProfileBloc.goToAddCoverImageScreen();
      },
      child: Column(
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              AppStrings.storeCoverImage,
              style: AppTextStyle.contentHeading0(
                textColor: AppColors.writingBlack0,
              ),
            ),
          ),
          Container(
            height: 120,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey[100],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: sellerStoreProfileBloc
                              .singleStoreInfoResponse.data!.coverImage ==
                          null ||
                      sellerStoreProfileBloc
                          .singleStoreInfoResponse.data!.coverImage!.isEmpty
                  ? Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.green.shade100,
                            Colors.green.shade200,
                          ],
                        ),
                      ),
                      child: Center(
                        child: Icon(
                          Icons.add_photo_alternate,
                          size: 40,
                          color: Colors.green.shade400,
                        ),
                      ),
                    )
                  : extendedImage(
                      sellerStoreProfileBloc
                          .singleStoreInfoResponse.data!.coverImage!,
                      customPlaceHolder: AppImages.bannerPlaceHolder,
                      context,
                      600,
                      300,
                      fit: BoxFit.cover,
                    ),
            ),
          ),
          verticalSizedBox(8),
          editStoreCoverImage(),
        ],
      ),
    );
  }

  //endregion

  //region Edit store cover image text
  Widget editStoreCoverImage() {
    return SizedBox(
      height: 20,
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          "Edit",
          style: AppTextStyle.contentText0(
            textColor: AppColors.writingBlack0,
            isUnderline: true,
          ),
        ),
      ),
    );
  }

  //endregion

  //region Edit store logo
  Widget editStoreLogo() {
    return SizedBox(
      height: 20,
      child: Text(
        AppStrings.editStoreLogo,
        style: AppTextStyle.contentText0(
            textColor: AppColors.writingBlack0, isUnderline: true),
      ),
    );
  }

  //endregion

//region Your StoreHandel is available
  Widget storeHandelAvailable() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ///Test store handle
        Visibility(
          visible:
              sellerStoreProfileBloc.singleStoreInfoResponse.data!.isTestStore!,
          child: StoreHandleAndUserName(
            isTestAccount: sellerStoreProfileBloc
                .singleStoreInfoResponse.data!.isTestStore!,
            onSearch: (data) {
              sellerStoreProfileBloc.isStoreHandleAvailable = data;
              //
              // widget.sellerOnBoardingBloc.onTextChangeCtrl.sink.add(true);
              //
              // widget.sellerOnBoardingBloc.isStoreHandleAvailable = data == null?null:!data;
              //print(data);
            },
            handleUserNameTextCtrl:
                sellerStoreProfileBloc.storeHandleNameTextCtrl,
            screenContext: context,
            isUserNameCheck: false,
            olderData: '',
            title:
                "${AppStrings.storeHandle} (${AppStrings.lowerCaseOnlyAllowed})",
            onChange: (data) {
              // sellerStoreProfileBloc.isStoreHandleAvailable = data;
            },
          ),
        ),

        ///Normal store handle
        Visibility(
          visible: !sellerStoreProfileBloc
              .singleStoreInfoResponse.data!.isTestStore!,
          child: StoreHandleAndUserName(
            onSearch: (data) {
              sellerStoreProfileBloc.isStoreHandleAvailable = data;

              // editUserProfileBloc.isUserNameAvailable = data;
              //print(data);
            },
            handleUserNameTextCtrl:
                sellerStoreProfileBloc.storeHandleNameTextCtrl,
            screenContext: context,
            isUserNameCheck: false,
            olderData: sellerStoreProfileBloc
                .singleStoreInfoResponse.data!.storehandle!,
            title:
                "${AppStrings.storeHandle} (${AppStrings.lowerCaseOnlyAllowed})",
            onChange: (data) {},
          ),
        ),
        // AppTitleAndOptions(
        //   title: AppStrings.storeHandle,
        //   option: AppTextFields.userNameTextField(
        //     context: context,
        //     textEditingController:sellerStoreProfileBloc.storeHandleNameTextCtrl,
        //     hintText:  AppStrings.enterStoreHandle,
        //     onChanged: (){
        //       sellerStoreProfileBloc.checkStoreHandle();
        //     }
        //   ),
        // ),
        // SellerStoreProfileCommonWidgets.storeHandleTextField(
        //   sellerStoreProfileBloc: sellerStoreProfileBloc,
        //   textCtrl: sellerStoreProfileBloc.storeHandleNameTextCtrl,
        //   hintText: AppStrings.enterStoreHandle,
        //   onChange: () {
        //     sellerStoreProfileBloc.checkStoreHandle();
        //   },
        //   heading: AppStrings.storeHandle,
        // ),
        //  Visibility(
        //  visible:sellerStoreProfileBloc.isStoreHandleAvailable != 2,
        //  child: AppCommonWidgets.validAndInvalid(textColor:sellerStoreProfileBloc.isStoreHandleAvailable == 0?AppColors.red:AppColors.brandGreen,
        // buttonText:  sellerStoreProfileBloc.isStoreHandleAvailable == 0 ? AppStrings.storeHandleIsNotAvailable : AppStrings.storeHandleIsAvailable,
        //  ),),
      ],
    );
  }

//endregion

  //region Choose business category and Text Field
  Widget chooseBusinessCategory() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTitleAndOptions(
          title: AppStrings.businessCategory,
          option: AppCommonWidgets.dropDownOptions(
              onTap: () {
                sellerStoreProfileBloc.onTapBusinessCategory();
              },
              context: context,
              hintText: AppStrings.city,
              value: sellerStoreProfileBloc.selectedCategory),
        )
      ],
    );
  }
  //endregion

  //region Manufacture
  Widget manufacture() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTitleAndOptions(
          title: "What is the business type?",
          option: AppCommonWidgets.dropDownOptions(
              onTap: () {
                sellerStoreProfileBloc.onTapBusinessType();
              },
              context: context,
              hintText: "Select or create a business type",
              value: sellerStoreProfileBloc.selectedBusinessType),
        ),
      ],
    );

    // return BusinessCategory(sellerStoreProfileBloc: sellerStoreProfileBloc,);
    // return Column(
    //   mainAxisSize: MainAxisSize.min,
    //   mainAxisAlignment: MainAxisAlignment.center,
    //   crossAxisAlignment: CrossAxisAlignment.start,
    //   children: [
    //     SellerStoreProfileCommonWidgets.businessCategoryTextField(
    //       sellerStoreProfileBloc: sellerStoreProfileBloc,
    //       textCtrl: SellerStoreProfileBloc.businessCategoryTextCtrl,
    //       hintText: AppStrings.category,
    //       onChange: () {},
    //       heading: AppStrings.businessCategory,
    //       focusNode: sellerStoreProfileBloc.businessCategoryFocusNode,
    //     ),
    //     //verticalSizedBox(10),
    //     // Visibility(
    //     //   visible: sellerStoreProfileBloc.businessCategoryFocusNode.hasFocus,
    //     //   child: Padding(
    //     //     padding: const EdgeInsets.only(bottom: 50, top: 10),
    //     //     child: Container(
    //     //       height: 150,
    //     //       padding: EdgeInsets.all(10),
    //     //       decoration: BoxDecoration(
    //     //           color: AppColors.white,
    //     //           borderRadius: const BorderRadius.all(Radius.circular(5)),
    //     //           border: Border.all(color: AppColors.lightWhite2)),
    //     //       child: ListView.builder(
    //     //         shrinkWrap: true,
    //     //         itemCount: sellerOnBoardingBloc.storeCategoryTextCtrl.text.isEmpty
    //     //             ? sellerOnBoardingBloc.businessCategoryList.length
    //     //             : sellerOnBoardingBloc.searchedCategoryList.length,
    //     //         itemBuilder: (BuildContext context, int index) {
    //     //           return InkWell(
    //     //             onTap: () {
    //     //               sellerOnBoardingBloc.onSelectCategory(
    //     //                 sellerOnBoardingBloc.storeCategoryTextCtrl.text.isEmpty
    //     //                     ? sellerOnBoardingBloc.businessCategoryList[index]
    //     //                     : sellerOnBoardingBloc.searchedCategoryList[index],
    //     //               );
    //     //             },
    //     //             // padding: EdgeInsets.zero,
    //     //             child: Column(
    //     //               mainAxisSize: MainAxisSize.min,
    //     //               mainAxisAlignment: MainAxisAlignment.center,
    //     //               crossAxisAlignment: CrossAxisAlignment.start,
    //     //               children: [
    //     //                 Text(
    //     //                   sellerOnBoardingBloc.storeCategoryTextCtrl.text.isEmpty
    //     //                       ? sellerOnBoardingBloc.businessCategoryList[index]
    //     //                       : sellerOnBoardingBloc.searchedCategoryList[index],
    //     //                   textAlign: TextAlign.left,
    //     //                   style: TextStyle(
    //     //                     fontFamily: "LatoRegular",
    //     //                     fontSize: 14,
    //     //                     fontWeight: FontWeight.w400,
    //     //                     color: AppColors.writingColor2,
    //     //                   ),
    //     //                 ),
    //     //                 verticalSizedBox(10),
    //     //                 divider(),
    //     //                 verticalSizedBox(10),
    //     //               ],
    //     //             ),
    //     //           );
    //     //         },
    //     //       ),
    //     //     ),
    //     //   ),
    //     // )
    //   ],
    // );
  }
  //endregion

//region Website Link
  Widget websiteLink() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppTitleAndOptions(
          title: AppStrings.websiteLinkOptional,
          option: ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: sellerStoreProfileBloc.urlTextCtrlList.length,
              itemBuilder: (BuildContext context, index) {
                return Padding(
                  padding: EdgeInsets.only(top: index == 0 ? 0 : 10),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: AppTextFields.websiteTextField(
                                context: context,
                                textEditingController: sellerStoreProfileBloc
                                    .urlTextCtrlList[index],
                                hintText: AppStrings.yourWebsiteAndOthers,
                                onChanged: () {
                                  sellerStoreProfileBloc.isChangedSomething =
                                      true;
                                  sellerStoreProfileBloc.checkUrlValidation(
                                      currentUrlCtrl: sellerStoreProfileBloc
                                          .urlTextCtrlList[index],
                                      fieldIndex: index);
                                }),
                          ),
                          sellerStoreProfileBloc.urlTextCtrlList.length == 1
                              ? Container()
                              : CupertinoButton(
                                  padding: EdgeInsets.zero,
                                  onPressed: () {
                                    sellerStoreProfileBloc
                                        .deleteStoreLinkApiCall(
                                            textFieldIndex: index);
                                    sellerStoreProfileBloc.isChangedSomething =
                                        true;
                                    // sellerStoreProfileBloc.removeUrlTextField(index);
                                  },
                                  child: const Icon(
                                    Icons.close,
                                    color: AppColors.appBlack,
                                  ))
                        ],
                      ),

                      ///If not-Valid
                      Visibility(
                        visible: sellerStoreProfileBloc.isUrlValid != null &&
                            !sellerStoreProfileBloc.isUrlValid! &&
                            sellerStoreProfileBloc.currentTextFieldIndex ==
                                index,
                        child: AppCommonWidgets.validAndInvalid(
                            textColor: AppColors.red,
                            buttonText: AppStrings.invalidUrl,
                            onTap: () {}),
                      ),
                    ],
                  ),
                );
              }),
        ),

        ///Add a new link
        Visibility(
          visible: sellerStoreProfileBloc.isUrlValid != null &&
              sellerStoreProfileBloc.isUrlValid!,
          child: AppCommonWidgets.validAndInvalid(
              textColor: AppColors.brandBlack,
              buttonText: "add a new link",
              onTap: () {
                sellerStoreProfileBloc.isChangedSomething = true;
                sellerStoreProfileBloc.addUrlTextField();
              }),
        )
      ],
    );
  }
//endregion
}
