import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:swadesic/features/common_buyer_seller_screen/save_or_discard/save_or_discard.dart';
import 'package:swadesic/features/common_buyer_seller_screen/search/search_screen.dart';
import 'package:swadesic/features/seller/add_image/add_image_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/model/store_onboarding_response/add_web_link_response.dart';
import 'package:swadesic/model/store_onboarding_response/business_category_response.dart';
import 'package:swadesic/model/store_onboarding_response/business_type_response.dart';
import 'package:swadesic/model/store_onboarding_response/store_handle_check_response.dart';
import 'package:swadesic/services/platform_file_upload_service.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/services/store_onboarding_services/store_onboarding_add_store_services.dart';
import 'package:swadesic/services/store_onboarding_services/store_onboarding_add_web_link.services.dart';
import 'package:swadesic/services/store_onboarding_services/store_onboarding_business_category_services.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/platform_image_picker.dart';
import 'package:swadesic/util/web_file_picker.dart';

enum SellerStoreProfileState { Loading, Success, Failed, Empty }

class SellerStoreProfileBloc {
  // region Common Variables
  BuildContext context;
  bool isChangedSomething = false;
  final String storeReference;
  int categoryId = 1;
  int currentTextFieldIndex = 0;
  bool? isUrlValid;

  ///Single store info
  late SingleStoreInfoResponse singleStoreInfoResponse;
  late SingleStoreInfoServices singleStoreInfoServices;

  ///Website links
  late AddWebLinkService addWebLinkService;
  late AddWebLinkResponse addWebLinkResponse;
  late BusinessCategoryService businessCategoryService;
  late BusinessCategoryResponse businessCategoryResponse;
  late BusinessTypeResponse businessTypeResponse;

  late String selectedCategory;
  late String selectedBusinessType;
  final ImagePicker picker = ImagePicker();
  final ImageCropper imageCrop = ImageCropper();
  late File? files;
  Uint8List? webImageBytes;
  String? webImageName;
  bool isImageSelected = false;

  // Cover image variables
  late File? coverImageFile;
  Uint8List? webCoverImageBytes;
  String? webCoverImageName;
  bool isCoverImageSelected = false;

  ///Store handle
  bool? isStoreHandleAvailable = false;
  late AddStoreService addStoreService;
  late SellerStoreHandleAvailableCheckResponse
      sellerStoreHandleAvailableCheckResponse;

  ///Business category
  late var uploadFileService = UploadFileService();
  late var platformFileUploadService = PlatformFileUploadService();

  // endregion

  //region Text Editing Controller
  TextEditingController storeNameTextCtrl = TextEditingController();
  TextEditingController storeHandleNameTextCtrl = TextEditingController();
  TextEditingController linkTextCtrl = TextEditingController();
  TextEditingController storeDesc = TextEditingController();
  List<TextEditingController> urlTextCtrlList = [TextEditingController()];

  //endregion

  //region Controller
  final storeProfileCtrl =
      StreamController<SellerStoreProfileState>.broadcast();
  //endregion

  // region | Constructor |
  SellerStoreProfileBloc(this.context, this.storeReference);
  // endregion

  // region Init
  void init() {
    singleStoreInfoServices = SingleStoreInfoServices();
    addStoreService = AddStoreService();
    addWebLinkService = AddWebLinkService();
    businessCategoryService = BusinessCategoryService();
    getSingleStoreInfo(storeReference: storeReference);
    getBusinessCategoryApiCall();
    getBusinessTypeApiCall();
  }
// endregion

  //region Get Business Category
  void getBusinessCategoryApiCall() async {
    try {
      //editProductCtrl.sink.add(HiddenProductState.Loading);
      businessCategoryResponse =
          await businessCategoryService.getBusinessCategory();
      //
      // for(int i = 0; i < businessCategoryResponse.businessCategoryList!.length;i++){
      //   businessCategoryList.add(businessCategoryResponse.businessCategoryList![i].categoryName.toString()) ;
      // }
      // editProductCtrl.sink.add(HiddenProductState.Success);
    } on ApiErrorResponseMessage catch (error) {
      //editProductCtrl.sink.add(HiddenProductState.Failed);
      CommonMethods.toastMessage(error.message!, context);
      return;
    } catch (error) {
      // // editProductCtrl.sink.add(HiddenProductState.Failed);
      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      // return;
    }
  }

//endregion

  //region Get Business type
  void getBusinessTypeApiCall() async {
    try {
      //editProductCtrl.sink.add(HiddenProductState.Loading);
      businessTypeResponse = await businessCategoryService.getBusinessType();
      //
      // for(int i = 0; i < businessCategoryResponse.businessCategoryList!.length;i++){
      //   businessCategoryList.add(businessCategoryResponse.businessCategoryList![i].categoryName.toString()) ;
      // }
      // editProductCtrl.sink.add(HiddenProductState.Success);
    } on ApiErrorResponseMessage catch (error) {
      //editProductCtrl.sink.add(HiddenProductState.Failed);
      CommonMethods.toastMessage(error.message!, context);
      return;
    } catch (error) {
      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      //
      // return;
    }
  }

//endregion

  //region Get Store Info Api call
  getSingleStoreInfo({required String storeReference}) async {
    try {
      //Loading
      storeProfileCtrl.sink.add(SellerStoreProfileState.Loading);
      //Api call
      singleStoreInfoResponse =
          await singleStoreInfoServices.getSingleStoreInfo(storeReference);

      //Remove "test_" from the start of the store handle if the store is a test store
      if (singleStoreInfoResponse.data!.isTestStore!) {
        singleStoreInfoResponse.data!.storehandle = singleStoreInfoResponse
            .data!.storehandle!
            .replaceFirst("test_", "");
      }
      //If there is website link then mark url valid to true
      if (singleStoreInfoResponse.data!.storelinks!.isNotEmpty &&
          singleStoreInfoResponse.data!.storelinks != null) {
        isUrlValid = true;
      }

      //Add data to text fields
      storeNameTextCtrl.text = singleStoreInfoResponse.data!.storeName!;
      storeHandleNameTextCtrl.text = singleStoreInfoResponse.data!.storehandle!;
      selectedCategory = singleStoreInfoResponse.data!.categoryName!;
      storeDesc.text = singleStoreInfoResponse.data!.storeDesc!;
      selectedCategory = singleStoreInfoResponse.data!.categoryName!;
      selectedBusinessType = singleStoreInfoResponse.data!.businessDescription!;
      // aboutBusinessTextCtrl.text = singleStoreInfoResponse.data!.!;

      ///Add url list to text field

      //Clear text editing controller
      //if it has store link then clear or else skip
      if (singleStoreInfoResponse.data!.storelinks!.isNotEmpty) {
        urlTextCtrlList.clear();
      }
      for (var data in singleStoreInfoResponse.data!.storelinks!) {
        urlTextCtrlList.add(TextEditingController(text: data.storeLink!));
      }
      //Success
      storeProfileCtrl.sink.add(SellerStoreProfileState.Success);

      //Go to single store screen
    } on ApiErrorResponseMessage catch (error) {
      //editProductCtrl.sink.add(HiddenProductState.Failed);
      CommonMethods.toastMessage(error.message!, context);
      return;
    } catch (error) {
      storeProfileCtrl.sink.add(SellerStoreProfileState.Failed);
      return;
    }
  }
//endregion

  //region Edit store detail
  editStoreDetail() async {
    try {
      ///Store handle is empty
      if (storeHandleNameTextCtrl.text.trim().isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.storeHandleCanNotBeEmpty, context);
      }

      ///Store handle check status is null. It means api crashed
      if (isStoreHandleAvailable == null) {
        return CommonMethods.toastMessage(
            AppStrings.unableToCheckStoreHandle, context);
      }

      ///Store handle is not available check
      if (isStoreHandleAvailable != null && isStoreHandleAvailable! == true) {
        return CommonMethods.toastMessage(
            AppStrings.storeHandleIsNotAvailable, context);
      }

      ///Store name
      if (storeNameTextCtrl.text.trim().isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.storeNameCanNotBeEmpty, context);
      }

      ///About product
      if (storeDesc.text.trim().isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.aboutYourBusinessCanNotEmpty, context);
      }
      //Check url validation
      if (isUrlValid != null && !isUrlValid!) {
        return CommonMethods.toastMessage(AppStrings.invalidUrl, context);
      }
      //If some of the product details are empty
      if (storeNameTextCtrl.text.isEmpty ||
          storeDesc.text.isEmpty ||
          selectedCategory.isEmpty ||
          storeHandleNameTextCtrl.text.isEmpty) {
        return CommonMethods.toastMessage(AppStrings.fieldEmpty, context);
      }
      //Loading
      storeProfileCtrl.sink.add(SellerStoreProfileState.Loading);

      ///Api call
      await singleStoreInfoServices.editStoreDetail(
          storeReference: singleStoreInfoResponse.data!.storeReference!,
          storeName: storeNameTextCtrl.text,
          storeDesc: storeDesc.text,
          businessType: selectedBusinessType,
          storeHandle:
              "${singleStoreInfoResponse.data!.isTestStore! ? "test_" : ""}${storeHandleNameTextCtrl.text}",
          categoryName: selectedCategory);

      ///Upload store image
      await startUpload();

      ///Upload store cover image
      await startCoverImageUpload();

      ///Add store link api call
      await addEditStoreLink();
      storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
      //Get updated store profile info
      await getSingleStoreInfo(storeReference: storeReference);
      //Send updated store info back
      if (context.mounted) {
        //print("Mounted True");

        Navigator.pop(context, singleStoreInfoResponse.data!);
      }
      //Store updated successfully
      CommonMethods.toastMessage(AppStrings.storeUpdateSuccessfully, context);
    } on ApiErrorResponseMessage {
      storeProfileCtrl.sink.add(SellerStoreProfileState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    } catch (error) {
      storeProfileCtrl.sink.add(SellerStoreProfileState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
//endregion

  //region Upload Store Logo
  Future<void> startUpload() async {
    try {
      //If image is not selected then return
      if (!isImageSelected) {
        return;
      }

      // Check if we're on web or mobile
      if (kIsWeb) {
        // Web platform
        if (webImageBytes == null || webImageName == null) {
          return;
        }

        // Upload using platform file upload service
        await platformFileUploadService.uploadStoreLogo(
          storeReference: singleStoreInfoResponse.data!.storeReference!,
          bytes: webImageBytes!,
          fileName: webImageName!,
        );
      } else {
        // Mobile platform
        if (files == null) {
          return;
        }

        String fileName = files!.path.split("/").last;

        // Upload using platform file upload service
        await platformFileUploadService.uploadStoreLogo(
          storeReference: singleStoreInfoResponse.data!.storeReference!,
          file: files!,
        );
      }
    } on ApiErrorResponseMessage catch (error) {
      storeProfileCtrl.sink.add(SellerStoreProfileState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    } catch (error) {
      storeProfileCtrl.sink.add(SellerStoreProfileState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    }
  }
  //endregion

  //region Upload Store Cover Image
  Future<void> startCoverImageUpload() async {
    try {
      //If cover image is not selected then return
      if (!isCoverImageSelected) {
        return;
      }

      // Check if we're on web or mobile
      if (kIsWeb) {
        // Web platform
        if (webCoverImageBytes == null || webCoverImageName == null) {
          return;
        }

        // Upload using platform file upload service
        await platformFileUploadService.uploadStoreCoverImage(
          storeReference: singleStoreInfoResponse.data!.storeReference!,
          bytes: webCoverImageBytes!,
          fileName: webCoverImageName!,
        );
      } else {
        // Mobile platform
        if (coverImageFile == null) {
          return;
        }

        // Upload using platform file upload service
        await platformFileUploadService.uploadStoreCoverImage(
          storeReference: singleStoreInfoResponse.data!.storeReference!,
          file: coverImageFile!,
        );
      }
    } on ApiErrorResponseMessage catch (error) {
      storeProfileCtrl.sink.add(SellerStoreProfileState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    } catch (error) {
      storeProfileCtrl.sink.add(SellerStoreProfileState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return;
    }
  }

  //endregion

  //region Check StoreHandle Available
  // checkStoreHandle()async{
  //   try{
  //     //If empty then return
  //     if(storeHandleNameTextCtrl.text.isEmpty || storeHandleNameTextCtrl.text == singleStoreInfoResponse.data!.storehandle!){
  //       isStoreHandleAvailable = 2;
  //       storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
  //       return;
  //     }
  //
  //     // makeInBharatCtrl.sink.add(LetsMakeInBharatState.Loading);
  //     sellerStoreHandleAvailableCheckResponse = await addStoreService.checkStoreHandle(storeHandleNameTextCtrl.text);
  //     //Show user name available or not
  //     if(sellerStoreHandleAvailableCheckResponse.available == "false"){
  //       isStoreHandleAvailable = 1;
  //       storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
  //     }
  //     else{
  //       isStoreHandleAvailable = 0;
  //       storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
  //
  //
  //     }
  //     // makeInBharatCtrl.sink.add(LetsMakeInBharatState.Success);
  //   }
  //   on ApiErrorResponseMessage {
  //     // makeInBharatCtrl.sink.add(LetsMakeInBharatState.Failed);
  //     CommonMethods.toastMessage(AppStrings.error, context);
  //     return;
  //   }
  //   catch(error){
  //     // makeInBharatCtrl.sink.add(LetsMakeInBharatState.Failed);
  //     CommonMethods.toastMessage(AppStrings.error, context);
  //     return;
  //   }
  //
  // }
//endregion

  //region Edit Store Links
  Future<void> addEditStoreLink() async {
    try {
      // Get web site links from text field list
      List<String> links = urlTextCtrlList.map((e) => e.text).toList();

      //If all field are empty then delete all links
      if (urlTextCtrlList.every((controller) => controller.text.isEmpty)) {
        //print("all empty");
        //Delete all links
        for (int i = 0; i <= urlTextCtrlList.length; i++) {
          //If storelinks
          if (singleStoreInfoResponse.data!.storelinks!.isEmpty) {
            continue;
          }
          await addWebLinkService.deleteStoreLink(
              storeLinkID:
                  singleStoreInfoResponse.data!.storelinks![i].storelinkid!);
        }
        return;
      }

      for (int i = 0; i <= links.length - 1; i++) {
        //If text field ix empty
        if (links[i].isEmpty) {
          continue;
        }

        //If index is within the Store link list then call edit api
        if (i <= singleStoreInfoResponse.data!.storelinks!.length - 1) {
          //print("Edit api called");

          await addWebLinkService.editStoreLink(
              storeLink: CommonMethods.addHttpAndHttps(url: links[i]),
              storeReference: storeReference,
              storeId: singleStoreInfoResponse.data!.storeid!,
              websiteName: CommonMethods.urlToWebsiteName(url: links[i]),
              storeLinkID:
                  singleStoreInfoResponse.data!.storelinks![i].storelinkid!);
        } else {
          //Add https if not added
          final modifiedUrl =
              links[i].startsWith('https') ? links[i] : 'https://${links[i]}';
          await addWebLinkService.addStoreLink(
            storeLink: modifiedUrl,
            storeReference: storeReference,
            linkName: CommonMethods.urlToWebsiteName(url: modifiedUrl),
          );

          //print("Add api called");
        }
      }
    } on ApiErrorResponseMessage catch (error) {
      //editProductCtrl.sink.add(HiddenProductState.Failed);
      CommonMethods.toastMessage(error.message.toString(), context);
      //
      // var snackBar = SnackBar(content: Text(error.message.toString()));
      // ScaffoldMessenger.of(context).showSnackBar(snackBar);
      return;
    } catch (error) {
      // editProductCtrl.sink.add(HiddenProductState.Failed);
      //print(error);
      CommonMethods.toastMessage(error.toString(), context);
      //
      // var snackBar = SnackBar(content: Text(error.toString()));
      // ScaffoldMessenger.of(context).showSnackBar(snackBar);
      return;
    }
  }
//endregion

  //region Delete store link api call
  deleteStoreLinkApiCall({required int textFieldIndex}) async {
    try {
      //Delete text field
      urlTextCtrlList.removeAt(textFieldIndex);
      //Check the index is within the storelink list or not
      //If index is within the storelink list then remove the object as well as remove the field
      if (textFieldIndex <=
          singleStoreInfoResponse.data!.storelinks!.length - 1) {
        //Remove the link by using link id
        //Api call
        await addWebLinkService.deleteStoreLink(
            storeLinkID: singleStoreInfoResponse
                .data!.storelinks![textFieldIndex].storelinkid!);

        //Remove the object from the list of store link
        singleStoreInfoResponse.data!.storelinks!.removeAt(textFieldIndex);
      }
      //Success
      storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
    } on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(error.toString(), context);
      return;
    }
  }
//endregion

  //region Add Url text field
  void addUrlTextField() {
    //Mark url valid to null
    isUrlValid = null;
    urlTextCtrlList.add(TextEditingController());
    storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
  }
  //endregion

  //region Remove url text field
  // void removeUrlTextField(int index){
  //   urlTextCtrlList.removeAt(index);
  //   storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
  // }
//endregion

  //region Go to Add Image Screen
  void goToAddImageScreen() async {
    if (kIsWeb) {
      // Web platform - use web file picker
      final imageData = await WebFilePicker.pickImage();
      if (imageData != null) {
        webImageBytes = imageData['bytes'] as Uint8List;
        webImageName = imageData['name'] as String;
        isImageSelected = true;
        isChangedSomething = true;
        storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
      }
    } else {
      // Mobile platform - use existing flow
      AppConstants.selectedSingleImage = null;
      var screen = const AddImageScreen(
        selectSingleImage: true,
        customTitle: "Store Logo",
      );
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(context, route).then((value) {}).then((value) {
        storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
        crop();
      });
    }
  }
//endregion

  //region Image crop
  crop() async {
    if (!kIsWeb) {
      // Only for mobile platform
      files = await CommonMethods.imageCrop(
          file: File(AppConstants.selectedSingleImage!.path),
          cropStyle: CropStyle.rectangle);
      if (files == null) {
        isImageSelected = false;
        storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
        return;
      }
      isImageSelected = true;
      isChangedSomething = true;
      storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
    }
  }
  //endregion

  //region Go to Add Cover Image Screen
  void goToAddCoverImageScreen() async {
    if (kIsWeb) {
      // Web platform - use web file picker
      final imageData = await WebFilePicker.pickImage();
      if (imageData != null) {
        webCoverImageBytes = imageData['bytes'] as Uint8List;
        webCoverImageName = imageData['name'] as String;
        isCoverImageSelected = true;
        isChangedSomething = true;
        storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
      }
    } else {
      // Mobile platform - use existing flow
      AppConstants.selectedSingleImage = null;
      var screen = const AddImageScreen(
        selectSingleImage: true,
        customTitle: "Store Cover Image",
      );
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(context, route).then((value) {}).then((value) {
        storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
        cropCoverImage();
      });
    }
  }
  //endregion

  //region Cover Image crop
  cropCoverImage() async {
    if (!kIsWeb) {
      // Only for mobile platform - use rectangle crop with 16:9 aspect ratio for cover images
      coverImageFile = await CommonMethods.imageCrop(
          file: File(AppConstants.selectedSingleImage!.path),
          cropStyle: CropStyle.rectangle,
          customAspectRatio: const CropAspectRatio(ratioX: 3, ratioY: 1));
      if (coverImageFile == null) {
        isCoverImageSelected = false;
        storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
        return;
      }
      isCoverImageSelected = true;
      isChangedSomething = true;
      storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
    }
  }
  //endregion

  //region On tap leading
  void onTapLeading() {
    //Check is anything change
    if (isChangedSomething) {
      openDialog();
    } else {
      Navigator.pop(context);
    }
  }
  //endregion

  //region Open dialog
  Future openDialog() {
    return CommonMethods.appDialogBox(
        context: context,
        widget: SaveOrDiscard(
          onTapSave: (value) async {
            await editStoreDetail();
          },
          previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.yourChangesIsNotYetSaved,
        ));
  }
//endregion

  //region On tap business category
  void onTapBusinessCategory() {
    List<String> dataList = [];
    for (var data in businessCategoryResponse.businessCategoryList!) {
      dataList.add(data.categoryName!);
    }
    var screen = SearchScreen(
      dataList: dataList,
      searchTitle: AppStrings.selectedBusinessCategory,
      isAddFeatureEnable: true,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route)
        .then((value) {
      if (value == null) {
        return;
      }
      selectedCategory = value;
      storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
    });
  }

//endregion

  //region On tap business type
  void onTapBusinessType() {
    List<String> dataList = [];
    for (var data in businessTypeResponse.data!) {
      dataList.add(data.businessType!);
    }
    var screen = SearchScreen(
      dataList: dataList,
      searchTitle: AppStrings.selectedBusinessType,
      isAddFeatureEnable: false,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route)
        .then((value) {
      if (value == null) {
        return;
      }
      selectedBusinessType = value;
      storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
    });
  }

//endregion

  //region Check URL validation
  // void checkUrlValidation(){
  //   //print(webSiteTextCtrl.text);
  //   if(webSiteTextCtrl.text.isEmpty){
  //     isEmailValid = null;
  //   }
  //   else{
  //     isEmailValid = CommonMethods.urlValidationCheck(url: webSiteTextCtrl.text);
  //   }
  //   refreshCtrl.sink.add(true);
  // }
//endregion

  //region Check url validation
  void checkUrlValidation(
      {required TextEditingController currentUrlCtrl, required fieldIndex}) {
    //Add current field index to currentTextFieldIndex
    currentTextFieldIndex = fieldIndex;
    //print(currentUrlCtrl.text);
    if (currentUrlCtrl.text.isEmpty) {
      isUrlValid = null;
    } else {
      isUrlValid = CommonMethods.urlValidationCheck(url: currentUrlCtrl.text);
    }
    storeProfileCtrl.sink.add(SellerStoreProfileState.Success);
  }
//endregion

//region Save
  void save() {}
//endregion
}
