import 'dart:async';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:safe_device/safe_device.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/features/bottom_navigations/store_bottom_navigation/store_bottom_navigation.dart';
import 'package:swadesic/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation.dart';
import 'package:swadesic/features/common_buyer_seller_screen/infected_device/infected_device.dart';
import 'package:swadesic/features/data_model/faq_data_model/faq_data_model.dart';
import 'package:swadesic/features/mandatory_update/mandatory_update.dart';
import 'package:swadesic/features/mobile_number_otp/intro_slider/intro_slider_screen.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_screen.dart';
import 'package:swadesic/model/base_url_response/base_url_response.dart';
import 'package:swadesic/services/app_link_services/logged_in_status_check_service.dart';
import 'package:swadesic/services/app_method_channel_service/app_method_channel_service.dart';
import 'package:swadesic/services/base_url_service/get_baseUrl_service.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/no_internet_handle/no_internet_handle.dart';

// enum SplashState { Loading, Success, Failed, Empty }

class SplashBloc {
  // region Common Methods
  BuildContext context;
  BaseUrlResponse baseUrlResponse = BaseUrlResponse(
    dev: EnvStructure(baseUrl: "https://e2e-77-175.ssdcloudindia.net/dev"),
    qa: EnvStructure(baseUrl: "https://e2e-77-175.ssdcloudindia.net/dev"),
    prod: EnvStructure(baseUrl: "https://e2e-65-177.ssdcloudindia.net/prod"),
    isMandatoryUpdate: false,
    appVersion: "1.0.0",
  );

  //Universal link
  // late UniversalLink universalLint = UniversalLink();
  // endregion
  //region Controller
  // final splashStateCtrl = StreamController<SplashState>.broadcast();

  //endregion

  // region | Constructor |
  SplashBloc(this.context);

  // endregion

  // region Init
  init() async {
    //Get app data
    await AppDataService().getAppData();
    //Get referral code from play store by Method channel
    AppMethodChannelService().getReferralCode();

    ///Check device safety if app is not in web
    if (!kIsWeb) {
      if (await SafeDevice.isJailBroken ||
          !await SafeDevice.isRealDevice &&
              AppConstants.appCurrentEnvironment != Environment.dev) {
        Widget screen = const InfectedDevice(
          message: AppStrings.deviceIsRooted,
        );
        var route = MaterialPageRoute(builder: (context) => screen);
        Navigator.push(AppConstants.globalNavigator.currentContext!, route);
        return;
      }
    }

    ///If web view
    // if (kIsWeb) {
    //   //print(AppConstants.appData);
    //   await AppDataService().getAppData();
    //   // AppConstants.baseUrl = "https://e2e-77-175.ssdcloudindia.net/dev";
    //   AppConstants.baseUrl = AppConstants.appCurrentEnvironment == Environment.dev
    //       ? "https://e2e-65-175.ssdcloudindia.net/dev"
    //       : "https://e2e-65-177.ssdcloudindia.net/prod";
    //   //Save to share in cache
    //   await AppDataService().addAppData();
    // }

    /// If DEV and PROD then check base url
    // else if (AppConstants.appCurrentEnvironment == Environment.prod){
    //    //Get Base url
    //    await getBaseUrl();
    // }
    //Get Base url
    await getBaseUrl();

    ///Check is mobile view, PROD and mandatory update is available then return else continue
    if (!kIsWeb &&
        await isMandatoryUpdateAvailable() &&
        AppConstants.appCurrentEnvironment == Environment.prod) {
      return;
    }

    ///Handle no internet
    NoInternetHandle().initializeConnectivity();
    //Handle user login status
    LoggedInStatusCheckService();

    ///Initialize FAQ data in background
    _initializeFaqData();

    ///Universal link initialize
    ///If app is not in mobile view then return
    // CommonMethods.checkIsAppInMobileView()?universalLint = UniversalLink():null;
  }

  // Initialize FAQ data in background
  void _initializeFaqData() async {
    try {
      // Get FAQ data model from context
      final faqDataModel = Provider.of<FaqDataModel>(context, listen: false);

      // Load FAQ data in background (don't await to avoid blocking splash)
      faqDataModel.loadFaqData().catchError((error) {
        // Silently handle errors - FAQ data will be loaded when user navigates to FAQ screen
        print('Background FAQ loading failed: $error');
      });
    } catch (e) {
      // Silently handle errors - FAQ data will be loaded when user navigates to FAQ screen
      print('FAQ initialization failed: $e');
    }
  }

  // endregion

  //region Check login status
  // Future<void> checkLoginStatus() async {
  //
  //   ///If is web view
  //   if (kIsWeb) {
  //     MobileStaticUserLogin();
  //
  //     // WebLoginFlow();
  //   }
  //
  //   ///Else mobile view
  //   else {
  //     MobileStaticUserLogin();
  //   }
  //
  //   //  ///1. Check if it is from web view and
  //   // ///2. having url params and
  //   // ///3. no user logged in yet
  //   // if(AppConstants.appData.userId == null && kIsWeb && Uri.parse(AppConstants.webIncomingUrl).path.isNotEmpty){
  //   //   return WebLoginFlow().assignStaticUserInfo();
  //   // }
  //   // ///If user id is is null then push user to sign in screen
  //   // if (AppConstants.appData.userId==null) {
  //   //   return openIntroSliderScreen();
  //   // }
  //   // ///Else go to user/store  bottom navigation screen
  //   // else {
  //   //   //Get updated token
  //   //   //Start timer to compare toke expiration time
  //   //   await AppTokenService().refreshToken();
  //   //   //If user view is true then go to user bottom navigation
  //   //   if(AppConstants.appData.isUserView!){
  //   //     goToUserBottomNavigation();
  //   //   }
  //   //   //Go to store bottom navigation
  //   //   else{
  //   //     goToStoreBottomNavigation();
  //   //   }
  //
  //   // }
  // }

  //endregion

  // region OpenHomeScreen
  void openLoginScreen() async {
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const MobileNumberOtpScreen()),
        (Route<dynamic> route) => false);

    // var screen = MobileNumberOtpScreen();
    // // var screen = const MyOrdersScreen();
    // var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }

// endregion

// region Go to intro slider screen
  void openIntroSliderScreen() async {
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const IntroSliderScreen()),
        (Route<dynamic> route) => false);

    // var screen = MobileNumberOtpScreen();
    // // var screen = const MyOrdersScreen();
    // var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }

// endregion

  // region Go To user bottom navigation
  void goToUserBottomNavigation() async {
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const UserBottomNavigation()),
        (Route<dynamic> route) => false);

    // var screen = UserBottomNavigation();
    // // var screen = const MyOrdersScreen();
    // var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }

// endregion

  // region Go To Store bottom navigation
  void goToStoreBottomNavigation() async {
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const StoreBottomNavigation()),
        (Route<dynamic> route) => false);

    // var screen = StoreBottomNavigation();
    // // var screen = const MyOrdersScreen();
    // var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }

// endregion

//region Get base url
  Future<void> getBaseUrl() async {
    try {
      //If web view then add default base url return
      if (kIsWeb) {
        AppConstants.baseUrl =
            AppConstants.appCurrentEnvironment == Environment.dev
                ? "https://e2e-77-175.ssdcloudindia.net/dev"
                : "https://e2e-65-177.ssdcloudindia.net/prod";
        // AppConstants.staticDevAccessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoyMDY2OTI3NDQ0LCJpYXQiOjE3NTE1Njc0NDQsImp0aSI6IjUxMTc4NGQwOTFiNTQ2NWZhNjdlYTg2OGFiZjRlZWRjIiwidXNlcl9pZCI6IlU5OTk5OTk5OTk5OTk5In0.VcWliH7xH52l5vfwQueOiU18auQR6C_aI1-s-b04ITY";
        // AppConstants.staticDevRefreshToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MjA2NjkyNzQ0NCwiaWF0IjoxNzUxNTY3NDQ0LCJqdGkiOiIyOTVmNGZiNGI1Yjk0YmYxODBlYTgyNmRiZjBmMDRiNSIsInVzZXJfaWQiOiJVOTk5OTk5OTk5OTk5OSJ9.a2KsMjQzr0HzOoYFNFbMimq9SBtZi8QCcPdfQ_rfLX0";
        // AppConstants.staticDevAccessTokenValidity = "2035-07-02T00:00:44.799258";
        // AppConstants.staticDevRefreshTokenValidity = "2035-07-02T00:00:44.799250";
        // AppConstants.staticProdAccessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoyMDY2OTMxNzEzLCJpYXQiOjE3NTE1NzE3MTMsImp0aSI6ImU1YThlN2FkNTRiNzQ1NzFhMTMwN2YwN2JkZGZmMjcyIiwidXNlcl9pZCI6IlU5OTk5OTk5OTk5OTk5In0.BQsMU0yIhCwcUQ7NTnIPZd1BS5qn4YbtjuIptn8Qd2k";
        // AppConstants.staticProdRefreshToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MjA2NjkzMTcxMywiaWF0IjoxNzUxNTcxNzEzLCJqdGkiOiI5NzJiNGFiMjRiYmY0MzFmYjcxZmU5NGIwNjgyZmIzMiIsInVzZXJfaWQiOiJVOTk5OTk5OTk5OTk5OSJ9.QQcRftwOcIeoWnbFJoMMNWIUQ0b9v4fwlixn07Fy_tY";
        // AppConstants.staticProdAccessTokenValidity = "2035-07-02T01:11:53.965421";
        // AppConstants.staticProdRefreshTokenValidity = "2035-07-02T01:11:53.965417";

        return;
      }
      // //Get app data
      // await AppDataService().getAppData();
      // //Add static url
      // AppConstants.appData.baseUrl = (AppConstants.appCurrentEnvironment == Environment.dev
      //     ?"https://e2e-77-175.ssdcloudindia.net/dev"
      //     : "https://e2e-65-177.ssdcloudindia.net/prod");
      // AppConstants.baseUrl = AppConstants.appData.baseUrl!;
      //Save to share in cache
      // await AppDataService().addAppData();
      baseUrlResponse = await BaseUrlService().getBaseUrl();
      //Add base url
      switch (AppConstants.appCurrentEnvironment) {
        case Environment.dev:
          AppConstants.appData.baseUrl = baseUrlResponse.dev!.baseUrl!;
          AppConstants.staticDevAccessToken = baseUrlResponse.dev!.staticAccessToken!;
          AppConstants.staticDevRefreshToken = baseUrlResponse.dev!.staticRefreshToken!;
          AppConstants.staticDevAccessTokenValidity = baseUrlResponse.dev!.staticAccessTokenValidity!;
          AppConstants.staticDevRefreshTokenValidity = baseUrlResponse.dev!.staticRefreshTokenValidity!;
          break;
        case Environment.qa:
          AppConstants.appData.baseUrl = baseUrlResponse.qa!.baseUrl!;
          break;
        case Environment.prod:
          AppConstants.appData.baseUrl = baseUrlResponse.prod!.baseUrl!;
          AppConstants.staticProdAccessToken = baseUrlResponse.prod!.staticAccessToken!;
          AppConstants.staticProdRefreshToken = baseUrlResponse.prod!.staticRefreshToken!;
          AppConstants.staticProdAccessTokenValidity = baseUrlResponse.prod!.staticAccessTokenValidity!;
          AppConstants.staticProdRefreshTokenValidity = baseUrlResponse.prod!.staticRefreshTokenValidity!;
          break;
        default:
          // Handle default case
          break;
      }

      // //Get app data
      // await AppDataService().getAppData();
      //Save base url in app data
      // AppConstants.appData.baseUrl = AppConstants.baseUrl;
      //Save to share in cache
      await AppDataService().addAppData();

      //print("base url is ${AppConstants.baseUrl}");
    } catch (error) {
      // //If api failed then get the data from cache memory and save in app constant base url
      // //Save to share in cache
      // await AppDataService().getAppData();
      // //Save base url in app data
      // AppConstants.appData.baseUrl = AppConstants.baseUrl;
      //Add base url
      switch (AppConstants.appCurrentEnvironment) {
        case Environment.dev:
          AppConstants.appData.baseUrl = baseUrlResponse.dev!.baseUrl!;
          AppConstants.staticDevAccessToken = baseUrlResponse.dev!.staticAccessToken!;
          AppConstants.staticDevRefreshToken = baseUrlResponse.dev!.staticRefreshToken!;
          AppConstants.staticDevAccessTokenValidity = baseUrlResponse.dev!.staticAccessTokenValidity!;
          AppConstants.staticDevRefreshTokenValidity = baseUrlResponse.dev!.staticRefreshTokenValidity!;
          break;
        case Environment.qa:
          AppConstants.appData.baseUrl = baseUrlResponse.qa!.baseUrl!;
          break;
        case Environment.prod:
          AppConstants.appData.baseUrl = baseUrlResponse.prod!.baseUrl!;
          AppConstants.staticProdAccessToken = baseUrlResponse.prod!.staticAccessToken!;
          AppConstants.staticProdRefreshToken = baseUrlResponse.prod!.staticRefreshToken!;
          AppConstants.staticProdAccessTokenValidity = baseUrlResponse.prod!.staticAccessTokenValidity!;
          AppConstants.staticProdRefreshTokenValidity = baseUrlResponse.prod!.staticRefreshTokenValidity!;
          break;
        default:
          // Handle default case
          break;
      }

      // //Get app data
      // await AppDataService().getAppData();
      // //Save base url in app data
      // AppConstants.appData.baseUrl = AppConstants.baseUrl;
      //Save to share in cache
      await AppDataService().addAppData();
    }
  }

//endregion make if else to switch

  //region Is mandatory update available
  Future<bool> isMandatoryUpdateAvailable() async {
    //Get app version
    var packageInfo = await PackageInfo.fromPlatform();
    int currentAppVersion =
        int.parse(packageInfo.version.replaceAll(RegExp('[.-]'), ''));

    //If not PROD then return false
    if (AppConstants.appCurrentEnvironment != Environment.prod) {
      return false;
    }
    //If baseUrlResponse is not null and app version is higher then the current app
    else if (baseUrlResponse.appVersion != null &&
        int.parse(baseUrlResponse.appVersion!.replaceAll(".", "")) >
            currentAppVersion) {
      //Push to the app update screen
      Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const MandatoryUpdate()),
          (Route<dynamic> route) => false);

      return true;
    } else {
      return false;
    }
  }

  //endregion

//region Dispose
  void dispose() {
    // splashStateCtrl.close();
  }
//endregion
}