import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_history_screen_common_widgets.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_history/buyer_search_history.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/search_initial_user_and_store_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/search_post_store_product_and_people/search_post_store_product_and_people_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/search_post_store_product_and_people/search_post_store_product_and_people_pagination.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/widgets/custom_image_container/custom_image_container.dart';
import 'package:swadesic/features/widgets/post_widgets/post_card.dart';
import 'package:swadesic/features/widgets/verified_badge.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_search_field/app_search_field.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:visibility_detector/visibility_detector.dart';

// region Search post store product and people
class SearchPostStoreProductAndPeopleScreen extends StatefulWidget {
  final EntityType entityType;
  final int limit;
  final bool isDetailView;
  final BuyerSearchBloc buyerSearchBloc;
  // final BuyerSearchHistoryResponse buyerSearchHistoryResponse;

  const SearchPostStoreProductAndPeopleScreen({
    Key? key,
    required this.entityType,
    required this.limit,
    this.isDetailView = true,
    required this.buyerSearchBloc,
  }) : super(key: key);

  @override
  _SearchPostStoreProductAndPeopleScreenState createState() =>
      _SearchPostStoreProductAndPeopleScreenState();
}
// endregion

class _SearchPostStoreProductAndPeopleScreenState
    extends State<SearchPostStoreProductAndPeopleScreen>
    with
        TickerProviderStateMixin,
        AutomaticKeepAliveClientMixin<SearchPostStoreProductAndPeopleScreen> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;
  // region Bloc and tab controller
  late SearchPostStoreProductAndPeopleBloc searchPostStoreProductAndPeopleBloc;

  // endregion

  // region Init
  @override
  void initState() {
    searchPostStoreProductAndPeopleBloc = SearchPostStoreProductAndPeopleBloc(
        context, widget.entityType, widget.limit, widget.buyerSearchBloc);
    searchPostStoreProductAndPeopleBloc.init();
    super.initState();
  }
  // endregion

  //region Dispose
  @override
  void dispose() {
    imageCache.clear();
    searchPostStoreProductAndPeopleBloc.dispose();
    super.dispose();
  }

  //endregion

  // region build
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) async {
        //print("${didPop} Tssssssssssssssssssssssssssssssssss");
      },
      child: GestureDetector(
        onTap: () {
          CommonMethods.closeKeyboard(context);
        },
        child: StreamBuilder<SearchPostStoreProductAndPeopleState>(
            stream: searchPostStoreProductAndPeopleBloc.searchResultCtrl.stream,
            initialData: SearchPostStoreProductAndPeopleState.Loading,
            builder: (context, snapshot) {
              if (snapshot.data ==
                  SearchPostStoreProductAndPeopleState.Loading) {
                return AppCommonWidgets.appCircularProgress();
              }
              if (snapshot.data ==
                  SearchPostStoreProductAndPeopleState.Success) {
                ///Post
                if (widget.entityType == EntityType.POST) {
                  return postLIst();
                }

                ///Store
                if (widget.entityType == EntityType.STORE) {
                  return storeLIst();
                }

                ///Product
                if (widget.entityType == EntityType.PRODUCT) {
                  return productListAndGreed();
                }

                ///User
                if (widget.entityType == EntityType.USER) {
                  return userList();
                }
              }

              if (snapshot.data == SearchPostStoreProductAndPeopleState.Empty) {
                return noResults();
              }
              return error();
            }),
      ),
    );
  }

  // endregion

  ///Post
//region Post list
  Widget postLIst() {
    return Column(
      children: [
        //Stores Text
        // BuyerSearchCommonWidgets.title(
        //   data: AppStrings.posts,
        // ),
        Expanded(
          child: RefreshIndicator(
            color: AppColors.brandBlack,
            onRefresh: () async {
              await searchPostStoreProductAndPeopleBloc
                  .getSearchResultApiCall();
            },
            child: ListView.builder(
                // physics: widget.isDetailView?const AlwaysScrollableScrollPhysics():const NeverScrollableScrollPhysics(),
                physics: const AlwaysScrollableScrollPhysics(),
                // shrinkWrap: true,
                itemCount:
                    searchPostStoreProductAndPeopleBloc.postList.length + 1,
                controller:
                    searchPostStoreProductAndPeopleBloc.scrollController,
                itemBuilder: (context, index) {
                  if (index <
                      searchPostStoreProductAndPeopleBloc.postList.length) {
                    return Consumer<PostDataModel>(
                      builder: (BuildContext context, PostDataModel value,
                          Widget? child) {
                        PostDetail postDetail = value.allPostDetailList
                            .firstWhere((element) =>
                                element.postOrCommentReference ==
                                searchPostStoreProductAndPeopleBloc
                                    .postList[index].postOrCommentReference);

                        return PostCard(
                          postDetail: postDetail,
                          customTitle: searchPostStoreProductAndPeopleBloc
                              .postList[index].contentHeaderText,
                          isCustomTitleVisible: true,
                          onTapDelete: () {
                            searchPostStoreProductAndPeopleBloc.confirmDelete(
                                postDetail: postDetail);
                          },
                          onTapDrawer: () {
                            searchPostStoreProductAndPeopleBloc.onTapDrawer(
                                postDetail: postDetail);
                          },
                          onTapEdit: () {
                            searchPostStoreProductAndPeopleBloc.goToEditPost(
                                postDetail: postDetail);
                          },
                          onTapHeart: () {
                            searchPostStoreProductAndPeopleBloc.onTapHeart(
                                postDetail: postDetail);
                          },
                          onTapShare: () {
                            searchPostStoreProductAndPeopleBloc.onTapShare(
                                postDetail: postDetail);
                          },
                          onTapProfileImage: () {
                            searchPostStoreProductAndPeopleBloc
                                .onTapUserOrStoreIcon(
                                    reference: postDetail
                                        .createdBy!.userOrStoreReference!);
                          },
                          onTapPost: () {
                            searchPostStoreProductAndPeopleBloc
                                .goToSinglePostView(
                                    postReference:
                                        postDetail.postOrCommentReference!);
                          },
                        );
                      },
                    );
                  } else {
                    return paginationProgress();
                  }
                }),
          ),
        ),
      ],
    );
  }
//endregion

  ///Stores
//region Stores list
  Widget storeLIst() {
    return Column(
      children: [
        //Store
        // BuyerSearchCommonWidgets.title(
        //   data: AppStrings.stores,
        // ),
        Expanded(
          child: RefreshIndicator(
            color: AppColors.brandBlack,
            onRefresh: () async {
              await searchPostStoreProductAndPeopleBloc
                  .getSearchResultApiCall();
            },
            child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                // physics: widget.isDetailView?const AlwaysScrollableScrollPhysics():const NeverScrollableScrollPhysics(),
                physics: const AlwaysScrollableScrollPhysics(),
                // shrinkWrap: true,
                itemCount:
                    searchPostStoreProductAndPeopleBloc.storeList.length + 1,
                controller:
                    searchPostStoreProductAndPeopleBloc.scrollController,
                itemBuilder: (context, index) {
                  if (index <
                      searchPostStoreProductAndPeopleBloc.storeList.length) {
                    return InkWell(
                      onTap: () {
                        searchPostStoreProductAndPeopleBloc.goToViewStoreScreen(
                            selectedStore: searchPostStoreProductAndPeopleBloc
                                .storeList[index]);
                      },
                      child: BuyerSearchCommonWidgets.searchedDataCard(
                        customImageContainerType:
                            CustomImageContainerType.store,
                        placeHolder: AppImages.storePlaceHolder,
                        context: context,
                        verifiedWidget: VerifiedBadge(
                          width: 15,
                          height: 15,
                          subscriptionType: searchPostStoreProductAndPeopleBloc
                              .storeList[index].subscriptionType,
                        ),
                        imageUrl: searchPostStoreProductAndPeopleBloc
                            .storeList[index].icon,
                        isStore: true,
                        heading: searchPostStoreProductAndPeopleBloc
                            .storeList[index].storehandle!,
                        title: searchPostStoreProductAndPeopleBloc
                            .storeList[index].storeName!,
                        subTitle:
                            "${searchPostStoreProductAndPeopleBloc.storeList[index].categoryName} • ${searchPostStoreProductAndPeopleBloc.storeList[index].storeDetails!.first.location}",
                        isCrossVisible: false,
                        onTapCard: () {
                          searchPostStoreProductAndPeopleBloc
                              .goToViewStoreScreen(
                                  selectedStore:
                                      searchPostStoreProductAndPeopleBloc
                                          .storeList[index]);
                        },
                      ),

                      // child: SizedBox(
                      //   height: 54,
                      //   child: Row(
                      //     mainAxisSize: MainAxisSize.max,
                      //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //     crossAxisAlignment: CrossAxisAlignment.center,
                      //     children: [
                      //       ClipRRect(
                      //         borderRadius: BorderRadius.all(Radius.circular(12)),
                      //         child: SizedBox(
                      //             height: 54,
                      //             width: 54,
                      //             child: store[index].icon != null
                      //                 ? extendedImage(store[index].icon!, context, 100, 100, cache: true, fit: BoxFit.cover)
                      //                 : SvgPicture.asset(AppImages.noStoreLogo)),
                      //       ),
                      //       horizontalSizedBox(15),
                      //       Column(
                      //         mainAxisSize: MainAxisSize.max,
                      //         mainAxisAlignment: MainAxisAlignment.spaceAround,
                      //         crossAxisAlignment: CrossAxisAlignment.start,
                      //         children: [
                      //           Text(
                      //             "${store[index].storeName}",
                      //             style: const TextStyle(color: AppColors.appBlack, fontSize: 14, fontFamily: "LatoBold", fontWeight: FontWeight.w700),
                      //           ),
                      //           Text(
                      //             "${store[index].storehandle}",
                      //             style: TextStyle(color: AppColors.appBlack, fontSize: 12, fontFamily: "LatoRegular", fontWeight: FontWeight.w400),
                      //           ),
                      //           Text(
                      //             "${store[index].location}",
                      //             style: TextStyle(color: AppColors.darkGray, fontSize: 12, fontFamily: "LatoBold", fontWeight: FontWeight.w700),
                      //           ),
                      //         ],
                      //       ),
                      //       Expanded(child: horizontalSizedBox(0)),
                      //     ],
                      //   ),
                      // ),
                    );
                  } else {
                    return paginationProgress();
                  }
                }),
          ),
        ),
      ],
    );
  }
//endregion

  ///Products
//region Product list and grid
  Widget productListAndGreed() {
    return Consumer<ProductDataModel>(
      builder: (BuildContext context, ProductDataModel value, Widget? child) {
        List<Product> productList = [];
        // Create a map for quick lookup based on productReference
        Map<String, Product> productMap = {
          for (var product in searchPostStoreProductAndPeopleBloc.productList)
            product.productReference!: product
        };

        // Filter and sort based on storeProductBloc.storeProductList order
        for (var storeProduct
            in searchPostStoreProductAndPeopleBloc.productList) {
          if (productMap.containsKey(storeProduct.productReference)) {
            productList.add(productMap[storeProduct.productReference]!);
          }
        }

        return Column(
          children: [
            // Product title without toggle icon - always using grid view
            // BuyerSearchCommonWidgets.title(
            //   data: AppStrings.products,
            // ),
            Expanded(
              child: productGrid(productList: productList),
            ),
          ],
        );
      },
    );
  }
//endregion

  //region Product list view
  Widget productListView({required List<Product> productList}) {
    return RefreshIndicator(
      color: AppColors.brandBlack,
      onRefresh: () async {
        await searchPostStoreProductAndPeopleBloc.getSearchResultApiCall();
      },
      child: ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          // physics: widget.isDetailView?const AlwaysScrollableScrollPhysics():const NeverScrollableScrollPhysics(),
          physics: const AlwaysScrollableScrollPhysics(),
          // shrinkWrap: true,
          itemCount: productList.length + 1,
          controller: searchPostStoreProductAndPeopleBloc.scrollController,
          itemBuilder: (context, index) {
            if (index < productList.length) {
              return BuyerSearchCommonWidgets.searchedDataCard(
                customImageContainerType: CustomImageContainerType.product,
                isProduct: true,
                placeHolder: AppImages.productPlaceHolder,
                context: context,
                heading:
                    "${productList[index].brandName!} ${productList[index].productName!}",
                imageUrl: productList[index].prodImages!.isEmpty
                    ? null
                    : productList[index].prodImages != null
                        ? productList[index].prodImages!.first.productImage
                        : null,
                title: productList[index].storehandle!,
                isCrossVisible: false,
                onTapCard: () {
                  searchPostStoreProductAndPeopleBloc.goToViewProductScreen(
                      product: productList[index], index: index);
                },
              );
            } else {
              return paginationProgress();
            }
          }),
    );
  }
//endregion

//region Product grid
  Widget productGrid({required List<Product> productList}) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        double width = constraints.maxWidth;
        return RefreshIndicator(
          color: AppColors.brandBlack,
          onRefresh: () async {
            await searchPostStoreProductAndPeopleBloc.getSearchResultApiCall();
          },
          child: SingleChildScrollView(
            child: Column(
              children: [
                GridView.builder(
                    addAutomaticKeepAlives: false,
                    addRepaintBoundaries: false,
                    padding: const EdgeInsets.only(bottom: 20),
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: productList.length,
                    // itemCount: 6,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      // childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 1.6),
                      crossAxisCount: 2,
                      mainAxisSpacing: 0,
                      crossAxisSpacing: 0,
                      mainAxisExtent: (width / 2) +
                          CommonMethods.textHeight(
                              context: context,
                              textStyle: AppTextStyle.contentHeading0(
                                  textColor: AppColors.appBlack)) +
                          CommonMethods.textHeight(
                              context: context,
                              textStyle: AppTextStyle.contentHeading0(
                                  textColor: AppColors.appBlack)) +
                          CommonMethods.textHeight(
                              context: context,
                              textStyle: AppTextStyle.access0(
                                  textColor: AppColors.appBlack)) +
                          5,
                    ),
                    itemBuilder: (BuildContext, index) {
                      // return Container(color: Colors.green,);

                      return InkWell(
                        onTap: () {
                          searchPostStoreProductAndPeopleBloc
                              .goToViewProductScreen(
                                  product: productList[index], index: index);
                        },
                        child: Opacity(
                          opacity: productList[index].inStock == 0 ? 0.4 : 1.0,
                          child: Container(
                              padding: EdgeInsets.zero,
                              decoration: BoxDecoration(
                                borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(10),
                                    topRight: Radius.circular(10)),
                                color: Colors.white,
                                border: Border.all(color: AppColors.lightGray2),
                                boxShadow: [
                                  BoxShadow(
                                    offset: const Offset(0, 1),
                                    blurRadius: 4,
                                    color: AppColors.appBlack.withOpacity(0.1),
                                  ),
                                ],
                              ),
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  ///Product card
                                  AppCommonWidgets.productCardInGrid(
                                    productImage: productList[index]
                                            .prodImages!
                                            .isEmpty
                                        ? null
                                        : productList[index].prodImages != null
                                            ? productList[index]
                                                .prodImages!
                                                .first
                                                .productImage
                                            : null,
                                    productBrand: productList[index].brandName!,
                                    productName:
                                        productList[index].productName!,
                                    sellingPrice: productList[index]
                                        .sellingPrice!
                                        .toString(),
                                    mrp:
                                        productList[index].mrpPrice!.toString(),
                                    context: context,
                                    screenWidth: width,
                                  ),

                                  // ///Shadow
                                  // widget.productList[index].inStock == 0
                                  //     ? Container(
                                  //   padding: EdgeInsets.zero,
                                  //   decoration: BoxDecoration(
                                  //     borderRadius: const BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
                                  //     color: AppColors.appWhite.withOpacity(0.2),
                                  //     border: Border.all(color: AppColors.lightGray2),
                                  //     boxShadow: [
                                  //       BoxShadow(
                                  //         offset: const Offset(0, 1),
                                  //         blurRadius: 5,
                                  //         color: AppColors.appBlack.withOpacity(0.2),
                                  //       ),
                                  //     ],
                                  //   ),
                                  // )
                                  //     : const SizedBox()
                                  ///Todo un-comment
                                  // Positioned(left: 5, top: 5, child: productRatings(index)),
                                  // Positioned(top: 0, right: 0, child: save(index))
                                ],
                              )),
                        ),
                        // child: Container(
                        //     padding: EdgeInsets.zero,
                        //     decoration: BoxDecoration(
                        //
                        //       borderRadius: BorderRadius.only(topLeft: Radius.circular(10),
                        //           topRight: Radius.circular(10)),
                        //       color: Colors.white,
                        //       border: Border.all(color: AppColors.lightGray2),
                        //       boxShadow: [
                        //         BoxShadow(
                        //           offset: const Offset(0, 1),
                        //           blurRadius: 5,
                        //           color: AppColors.appBlack.withOpacity(0.2),
                        //         ),
                        //       ],
                        //     ),
                        //     child: Stack(
                        //       alignment: Alignment.center,
                        //       children: [
                        //         Column(
                        //           mainAxisAlignment: MainAxisAlignment.start,
                        //           crossAxisAlignment: CrossAxisAlignment.start,
                        //           mainAxisSize: MainAxisSize.min,
                        //           children: [
                        //             Expanded(
                        //                 child: productImage(index)
                        //             ),
                        //             SizedBox(
                        //               height: 70,
                        //               child: Column(
                        //                 mainAxisAlignment: MainAxisAlignment.center,
                        //                 crossAxisAlignment: CrossAxisAlignment.start,
                        //                 mainAxisSize: MainAxisSize.min,
                        //                 children: [
                        //                   verticalSizedBox(5),
                        //                   Expanded(child: productName(index)),
                        //                   productPrice(index),
                        //                   verticalSizedBox(5)
                        //                 ],
                        //               ),
                        //             )
                        //
                        //
                        //           ],
                        //         ),
                        //         Positioned(
                        //             left: 5,
                        //             top: 5,
                        //
                        //             child: productRatings(index)),
                        //         Positioned(
                        //             top: 0,
                        //             right: 0,
                        //             child: save(index))
                        //       ],
                        //     )
                        // ),
                      );
                    }),
                paginationProgress(),
              ],
            ),
          ),
        );
        // return Column(
        //   children: [
        //     GridView.builder(
        //         addAutomaticKeepAlives: false,
        //         addRepaintBoundaries: false,
        //         padding: const EdgeInsets.only(bottom: 20),
        //         physics: const NeverScrollableScrollPhysics(),
        //         shrinkWrap: true,
        //         itemCount:productList.length,
        //         // itemCount: 6,
        //         gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        //           // childAspectRatio: MediaQuery.of(context).size.width / (MediaQuery.of(context).size.height / 1.6),
        //           crossAxisCount: 2,
        //           mainAxisSpacing: 0,
        //           crossAxisSpacing: 0,
        //           mainAxisExtent: (width / 2) +
        //               CommonMethods.textHeight(
        //                   context: context,
        //                   textStyle: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)) +
        //               CommonMethods.textHeight(
        //                   context: context,
        //                   textStyle: AppTextStyle.contentHeading0(textColor: AppColors.appBlack)) +
        //               CommonMethods.textHeight(
        //                   context: context,
        //                   textStyle: AppTextStyle.access0(textColor: AppColors.appBlack)) + 5,
        //         ),
        //         itemBuilder:(BuildContext,index){
        //           // return Container(color: Colors.green,);
        //
        //           return InkWell(
        //             onTap: (){
        //               searchPostStoreProductAndPeopleBloc.goToViewProductScreen(product:productList[index],index: index );
        //             },
        //             child:Opacity(
        //               opacity: productList[index].inStock == 0?0.4:1.0,
        //               child: Container(
        //                   padding: EdgeInsets.zero,
        //                   decoration: BoxDecoration(
        //                     borderRadius: const BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
        //                     color: Colors.white,
        //                     border: Border.all(color: AppColors.lightGray2),
        //                     boxShadow: [
        //                       BoxShadow(
        //                         offset: const Offset(0, 1),
        //                         blurRadius: 4,
        //                         color: AppColors.appBlack.withOpacity(0.1),
        //                       ),
        //                     ],
        //                   ),
        //                   child: Stack(
        //                     alignment: Alignment.center,
        //                     children: [
        //                       ///Product card
        //                       AppCommonWidgets.productCardInGreed(
        //                         productImage: productList[index].prodImages!.isEmpty
        //                             ? null
        //                             : productList[index].prodImages != null
        //                             ? productList[index].prodImages!.first.productImage
        //                             : null,
        //                         productBrand:productList[index].brandName!,
        //                         productName: productList[index].productName!,
        //                         sellingPrice: productList[index].sellingPrice!.toString(),
        //                         mrp: productList[index].mrpPrice!.toString(),
        //                         context: context, screenWidth: width,
        //                       ),
        //
        //
        //                       // ///Shadow
        //                       // widget.productList[index].inStock == 0
        //                       //     ? Container(
        //                       //   padding: EdgeInsets.zero,
        //                       //   decoration: BoxDecoration(
        //                       //     borderRadius: const BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
        //                       //     color: AppColors.appWhite.withOpacity(0.2),
        //                       //     border: Border.all(color: AppColors.lightGray2),
        //                       //     boxShadow: [
        //                       //       BoxShadow(
        //                       //         offset: const Offset(0, 1),
        //                       //         blurRadius: 5,
        //                       //         color: AppColors.appBlack.withOpacity(0.2),
        //                       //       ),
        //                       //     ],
        //                       //   ),
        //                       // )
        //                       //     : const SizedBox()
        //                       ///Todo un-comment
        //                       // Positioned(left: 5, top: 5, child: productRatings(index)),
        //                       // Positioned(top: 0, right: 0, child: save(index))
        //                     ],
        //                   )),
        //             ),
        //             // child: Container(
        //             //     padding: EdgeInsets.zero,
        //             //     decoration: BoxDecoration(
        //             //
        //             //       borderRadius: BorderRadius.only(topLeft: Radius.circular(10),
        //             //           topRight: Radius.circular(10)),
        //             //       color: Colors.white,
        //             //       border: Border.all(color: AppColors.lightGray2),
        //             //       boxShadow: [
        //             //         BoxShadow(
        //             //           offset: const Offset(0, 1),
        //             //           blurRadius: 5,
        //             //           color: AppColors.appBlack.withOpacity(0.2),
        //             //         ),
        //             //       ],
        //             //     ),
        //             //     child: Stack(
        //             //       alignment: Alignment.center,
        //             //       children: [
        //             //         Column(
        //             //           mainAxisAlignment: MainAxisAlignment.start,
        //             //           crossAxisAlignment: CrossAxisAlignment.start,
        //             //           mainAxisSize: MainAxisSize.min,
        //             //           children: [
        //             //             Expanded(
        //             //                 child: productImage(index)
        //             //             ),
        //             //             SizedBox(
        //             //               height: 70,
        //             //               child: Column(
        //             //                 mainAxisAlignment: MainAxisAlignment.center,
        //             //                 crossAxisAlignment: CrossAxisAlignment.start,
        //             //                 mainAxisSize: MainAxisSize.min,
        //             //                 children: [
        //             //                   verticalSizedBox(5),
        //             //                   Expanded(child: productName(index)),
        //             //                   productPrice(index),
        //             //                   verticalSizedBox(5)
        //             //                 ],
        //             //               ),
        //             //             )
        //             //
        //             //
        //             //           ],
        //             //         ),
        //             //         Positioned(
        //             //             left: 5,
        //             //             top: 5,
        //             //
        //             //             child: productRatings(index)),
        //             //         Positioned(
        //             //             top: 0,
        //             //             right: 0,
        //             //             child: save(index))
        //             //       ],
        //             //     )
        //             // ),
        //           );
        //
        //         }
        //     ),
        //  paginationProgress(),
        //   ],
        // );
      },
    );
  }
//endregion

  ///User
//region Product list
  Widget userList() {
    return Column(
      children: [
        //People Text
        // BuyerSearchCommonWidgets.title(
        //   data: AppStrings.people,
        // ),
        Expanded(
          child: RefreshIndicator(
            color: AppColors.brandBlack,
            onRefresh: () async {
              await searchPostStoreProductAndPeopleBloc
                  .getSearchResultApiCall();
            },
            child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                // physics: widget.isDetailView?const AlwaysScrollableScrollPhysics():const NeverScrollableScrollPhysics(),
                physics: const AlwaysScrollableScrollPhysics(),
                // shrinkWrap: true,
                itemCount:
                    searchPostStoreProductAndPeopleBloc.userList.length + 1,
                controller:
                    searchPostStoreProductAndPeopleBloc.scrollController,
                itemBuilder: (context, index) {
                  if (index <
                      searchPostStoreProductAndPeopleBloc.userList.length) {
                    return BuyerSearchCommonWidgets.searchedDataCard(
                      customImageContainerType: CustomImageContainerType.user,
                      isUser: true,
                      placeHolder: AppImages.userPlaceHolder,
                      context: context,
                      verifiedWidget: VerifiedBadge(
                        width: 15,
                        height: 15,
                        subscriptionType: searchPostStoreProductAndPeopleBloc
                            .userList[index].subscriptionType,
                      ),
                      heading:
                          "${searchPostStoreProductAndPeopleBloc.userList[index].userName!}",
                      imageUrl: searchPostStoreProductAndPeopleBloc
                          .userList[index].icon,
                      title: searchPostStoreProductAndPeopleBloc
                          .userList[index].displayName!,
                      isCrossVisible: false,
                      onTapCard: () {
                        searchPostStoreProductAndPeopleBloc
                            .goToUserProfileScreen(
                                userReference:
                                    searchPostStoreProductAndPeopleBloc
                                        .userList[index].userReference!);
                      },
                    );
                  } else {
                    return paginationProgress();
                  }
                }),
          ),
        ),
      ],
    );
  }
//endregion

//region No results
  Widget noResults() {
    return Center(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(AppImages.noSearchResultIcon),
        verticalSizedBox(7.12),
        Text(
          AppStrings.noMatchingSearchResult,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        ),
      ],
    ));
  }

//endregion

//region Error
  Widget error() {
    return Center(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(AppImages.noSearchResultIcon),
        verticalSizedBox(7.12),
        Text(
          AppStrings.yourSearch,
          style: AppTextStyle.contentText0(textColor: AppColors.writingBlack0),
        ),
        verticalSizedBox(4.5),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              AppStrings.noWorry,
              style:
                  AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
            ),
            horizontalSizedBox(3),
            SvgPicture.asset(AppImages.smileEmoji)
          ],
        ),
      ],
    ));
  }
//endregion

//region Pagination progress
  Widget paginationProgress() {
    return StreamBuilder<SearchResultPaginationState>(
        stream: searchPostStoreProductAndPeopleBloc
            .searchPostStoreProductAndPeoplePagination
            .paginationStateCtrl
            .stream,
        initialData: SearchResultPaginationState.Loading,
        builder: (context, snapshot) {
          //Loading
          if (snapshot.data == SearchResultPaginationState.Loading) {
            return VisibilityDetector(
                key: UniqueKey(),
                onVisibilityChanged: (visibilityInfo) {
                  var visiblePercentage = visibilityInfo.visibleFraction * 100;
                  if (visiblePercentage == 100) {
                    searchPostStoreProductAndPeopleBloc
                        .searchPostStoreProductAndPeoplePagination
                        .onPaginationVisible();
                  }
                },
                child: AppCommonWidgets.appCircularProgress(
                    isPaginationProgress: true));
          }
          return const SizedBox();
        });
  }
//endregion
}
