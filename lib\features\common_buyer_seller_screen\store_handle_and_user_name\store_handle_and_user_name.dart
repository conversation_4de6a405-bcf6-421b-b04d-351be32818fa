import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:swadesic/features/common_buyer_seller_screen/store_handle_and_user_name/store_handle_and_user_name_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_widgets.dart';

//region StoreHandleAndUserName
class StoreHandleAndUserName extends StatefulWidget {
  final Function(bool?) onSearch;
  final Function(String?) onChange;
  final BuildContext screenContext;
  final TextEditingController handleUserNameTextCtrl;
  final String olderData;
  final bool isUserNameCheck;
  final String title;
  final bool isTestAccount;
  const StoreHandleAndUserName(
      {super.key,
      required this.onSearch,
      required this.handleUserNameTextCtrl,
      required this.screenContext,
      required this.olderData,
      required this.isUserNameCheck,
      required this.title,
      this.isTestAccount = false,
      required this.onChange});

  @override
  State<StoreHandleAndUserName> createState() => _StoreHandleAndUserNameState();
}
//endregion

class _StoreHandleAndUserNameState extends State<StoreHandleAndUserName>
    with AutomaticKeepAliveClientMixin<StoreHandleAndUserName> {
  //Keep alive
  @override
  bool get wantKeepAlive => true;

  //region Bloc
  late StoreHandleAndUserNameBloc storeHandleAndUserNameBloc;
  //endregion
  //region Init
  @override
  void initState() {
    storeHandleAndUserNameBloc = StoreHandleAndUserNameBloc(
        context,
        widget.onSearch,
        widget.handleUserNameTextCtrl,
        widget.screenContext,
        widget.olderData,
        widget.isUserNameCheck,
        widget.isTestAccount,
        widget.onChange);
    storeHandleAndUserNameBloc.init();
    super.initState();
  }
  //endregion

  //region Dispose

  @override
  void dispose() {
    storeHandleAndUserNameBloc.dispose();
    super.dispose();
  }
  //endregion

  //region Build
  @override
  Widget build(BuildContext context) {
    super.build(context); // Call super.build to satisfy @mustCallSuper
    return Center(child: body());
  }
  //endregion

  //region Body
  Widget body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ///User name
        Visibility(
          visible: storeHandleAndUserNameBloc.isUserNameCheck,
          child: AppTitleAndOptions(
            title: widget.title,
            option: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 10), // Added spacing
                TextFormField(
                  maxLines: 1,
                  minLines: 1,
                  textCapitalization: TextCapitalization.none,
                  textAlign: TextAlign.start,
                  keyboardType: TextInputType.text,
                  controller: storeHandleAndUserNameBloc.handleUserNameTextCtrl,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[a-z0-9_.]')),
                    // FilteringTextInputFormatter.deny(RegExp(r'[ A-Z]')),
                    LengthLimitingTextInputFormatter(35),
                  ],
                  scrollPadding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).size.height * 0.2),
                  style:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack),
                  decoration: AppTextFields.appInputDecoration(
                      hintText: AppStrings.userName, prefix: null),
                ),
              ],
            ),
            // option: AppTextFields.userNameTextField(
            //   // prefix: widget.isTestAccount?"test_":null,
            //     context: context,
            //     textEditingController:storeHandleAndUserNameBloc.handleUserNameTextCtrl,
            //     hintText:  AppStrings.userName,
            //     maxEntry: 35,
            //
            //     onChanged: (value){
            //       widget.onChange(value);
            //
            //       storeHandleAndUserNameBloc.onChangeText(value: value);
            //       // editUserProfileBloc.isChanged = true;
            //       // editUserProfileBloc.checkUserName();
            //     }
            // ),
          ),
        ),

        ///Store handle
        Visibility(
          visible: !storeHandleAndUserNameBloc.isUserNameCheck,
          child: AppTitleAndOptions(
            // title:"${AppStrings.chooseAStoreHandle} ${AppStrings.youCanEdit}",
            title: widget.title,
            option: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 0), // Added spacing
                TextFormField(
                  maxLines: 1,
                  minLines: 1,
                  textCapitalization: TextCapitalization.none,
                  textAlign: TextAlign.start,
                  keyboardType: TextInputType.text,
                  controller: storeHandleAndUserNameBloc.handleUserNameTextCtrl,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[a-z0-9_.]')),
                    // FilteringTextInputFormatter.deny(RegExp(r'[ A-Z]')),
                    LengthLimitingTextInputFormatter(35),
                  ],
                  scrollPadding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).size.height * 0.2),
                  style:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack),
                  decoration: AppTextFields.appInputDecoration(
                      hintText: widget.isTestAccount
                          ? AppStrings.enterYourTestStoreHandle.toLowerCase()
                          : AppStrings.enterYourStoreHandle,
                      prefix: widget.isTestAccount ? "test_" : null),
                ),
              ],
            ),
            // option: AppTextFields.userNameTextField(
            //     prefix: widget.isTestAccount?"test_ ":null,
            //     context: context,
            //     minLines: 1,
            //     maxLines: 1,
            //     maxEntry: 35,
            //     textEditingController:storeHandleAndUserNameBloc.handleUserNameTextCtrl,
            //     hintText:widget.isTestAccount?AppStrings.enterYourTestStoreHandle.toLowerCase():AppStrings.enterYourStoreHandle,
            //     onChanged: (value){
            //       // widget.onChange(value);
            //       // storeHandleAndUserNameBloc.onChangeText(value: value);
            //
            //     }
            //     // onChanged: (){
            //     //   widget.sellerOnBoardingBloc.storeHandleTextCtrl.text = widget.sellerOnBoardingBloc.storeHandleTextCtrl.text.toLowerCase();
            //     //   widget.sellerOnBoardingBloc.storeHandleTextCtrl.selection = TextSelection(
            //     //       baseOffset: widget.sellerOnBoardingBloc.storeHandleTextCtrl.text.length,
            //     //       extentOffset: widget.sellerOnBoardingBloc.storeHandleTextCtrl.text.length);
            //     //   widget.sellerOnBoardingBloc.onTextChange();
            //     //   widget.sellerOnBoardingBloc.checkStoreHandle();
            //     // }
            // ),
          ),
        ),

        validationMessage(),
      ],
    );
  }
  //endregion

  //region Validation message
  Widget validationMessage() {
    return StreamBuilder<StoreHandleAndUserNameState>(
        stream:
            storeHandleAndUserNameBloc.storeHandleAndUserNameStateCtrl.stream,
        initialData: StoreHandleAndUserNameState.Success,
        builder: (context, snapshot) {
          if (snapshot.data == StoreHandleAndUserNameState.Success) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // //Already exist
                // Visibility(
                //     visible: storeHandleAndUserNameBloc.isAvailable == null,
                //     child: AppCommonWidgets.validAndInvalid(textColor: AppColors.red, buttonText:"Hello")
                // ),
                //Already exist
                Visibility(
                    visible: storeHandleAndUserNameBloc.isAvailable != null &&
                        storeHandleAndUserNameBloc.isAvailable!,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: AppCommonWidgets.validAndInvalid(
                          textColor: AppColors.red,
                          buttonText: widget.isUserNameCheck
                              ? "${storeHandleAndUserNameBloc.handleUserNameTextCtrl.text} is unavailable"
                              : "${storeHandleAndUserNameBloc.handleUserNameTextCtrl.text} is unavailable"),
                    )),
                //Available
                Visibility(
                    visible: storeHandleAndUserNameBloc.isAvailable != null &&
                        !storeHandleAndUserNameBloc.isAvailable!,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: AppCommonWidgets.validAndInvalid(
                          textColor: AppColors.brandGreen,
                          buttonText: widget.isUserNameCheck
                              ? "${storeHandleAndUserNameBloc.handleUserNameTextCtrl.text} is available"
                              : "${storeHandleAndUserNameBloc.handleUserNameTextCtrl.text} is available"),
                    )),
              ],
            );
          }

          return const SizedBox();
        });
  }
//endregion
}
