import 'package:flutter/cupertino.dart';
import 'package:swadesic/features/common_buyer_seller_screen/store_handle_and_user_name/store_handle_and_user_name.dart';
import 'package:swadesic/features/seller/labels/label_common_widgets.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding/seller_onboarding_bloc.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding/seller_onboarding_common_widgets.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

//region Lets make in bharat
class LetsMakeInBharat extends StatefulWidget {
  final bool isTestStore;
  final SellerOnBoardingBloc sellerOnBoardingBloc;
  const LetsMakeInBharat(
      {Key? key, required this.sellerOnBoardingBloc, required this.isTestStore})
      : super(key: key);

  @override
  State<LetsMakeInBharat> createState() => _LetsMakeInBharatState();
}
//endregion

class _LetsMakeInBharatState extends State<LetsMakeInBharat> {
  //region Bloc
  // late LetsMakeInBharatBloc letsMakeInBharatBloc;
  //endregion
  //region init
  // @override
  // void initState() {
  //   letsMakeInBharatBloc = LetsMakeInBharatBloc(context,widget.sellerOnBoardingBloc);
  //   letsMakeInBharatBloc.init();
  //   super.initState();
  // }
  //endregion
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: SizedBox(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            // shrinkWrap: true,
            // padding: EdgeInsets.zero,
            children: [
              letTheWorldText(),
              verticalSizedBox(20),
              socialMediaIcons(),
              verticalSizedBox(20),
              shareLink(),
              chooseStoreHandle(),
              ownedLabel(),
              makeInBharat(),
              verticalSizedBox(35),
              // AppCommonWidgets.bottomListSpace(context: context),
            ],
          ),
        ),
      ),
    );
  }
//endregion

  //region Let the world know
  // Widget letTheWorldKnow() {
  //   return SingleChildScrollView(
  //     child: Column(
  //       mainAxisSize: MainAxisSize.min,
  //       // shrinkWrap: true,
  //       // padding: EdgeInsets.zero,
  //       children: [
  //         letTheWorldText(),
  //         verticalSizedBox(20),
  //         socialMediaIcons(),
  //         verticalSizedBox(20),
  //         shareLink(),
  //         SizedBox(
  //           height: MediaQuery.of(context).size.height / 8,
  //         ),
  //         chooseStoreHandle(),
  //         SizedBox(
  //           height: MediaQuery.of(context).size.height / 8,
  //         ),
  //         ownedLabel(),
  //         makeInBharat(),
  //
  //         AppCommonWidgets.bottomListSpace(context: context),
  //       ],
  //     ),
  //   );
  // }

  //endregion

  //region Let the world Know Text
  Widget letTheWorldText() {
    return SellerOnBoardingCommonWidgets.screenTitle(
        value: AppStrings.letTheWorldKnow);
    //
    // return Text(
    //   AppStrings.letTheWorldKnow,
    //   textAlign: TextAlign.center,
    //   style: TextStyle(
    //     fontFamily: "LatoRegular",
    //     fontWeight: FontWeight.w400,
    //     fontSize: 24,
    //     color: AppColors.appBlack,
    //   ),
    // );
  }

  //endregion

  //region SocialMedia Icons
  Widget socialMediaIcons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Image.asset(
        AppImages.socialMediaIcons,
        fit: BoxFit.contain,
        cacheWidth: (900 * 0.95).toInt(),
        cacheHeight: (162 * 0.95).toInt(),
      ),
    );
  }

  //endregion

  // region Share your link
  Widget shareLink() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          AppStrings.shareYourLink,
          textAlign: TextAlign.center,
          style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(10),
        StreamBuilder<bool>(
            stream: widget.sellerOnBoardingBloc.onTextChangeCtrl.stream,
            builder: (context, snapshot) {
              // //print(widget.letsMakeInBharatBloc.sellerOnBoardingBloc.storeHandleTextCtrl.text);
              return Text(
                "www.swadesic.com/${widget.isTestStore ? "test_" : ""}${widget.sellerOnBoardingBloc.storeHandleTextCtrl.text.isEmpty ? "store_handle" : widget.sellerOnBoardingBloc.storeHandleTextCtrl.text}/",
                maxLines: 2,
                textAlign: TextAlign.center,
                style:
                    AppTextStyle.heading2Bold(textColor: AppColors.brandBlack),
              );
            }),
      ],
    );
  }

  //endregion

  //region Choose a store handle and text field
  Widget chooseStoreHandle() {
    return StreamBuilder<bool>(
        stream: widget.sellerOnBoardingBloc.storeHandleCtrl.stream,
        initialData: true,
        builder: (context, snapshot) {
          return Container(
            margin:
                EdgeInsets.only(top: MediaQuery.of(context).size.height / 8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                StoreHandleAndUserName(
                  isTestAccount: widget.isTestStore,
                  onSearch: (data) {
                    widget.sellerOnBoardingBloc.onTextChangeCtrl.sink.add(true);

                    widget.sellerOnBoardingBloc.isStoreHandleAvailable =
                        data == null ? null : !data;
                    // editUserProfileBloc.isUserNameAvailable = data;
                    //print(data);
                  },
                  handleUserNameTextCtrl:
                      widget.sellerOnBoardingBloc.storeHandleTextCtrl,
                  screenContext: context,
                  isUserNameCheck: false,
                  olderData: '',
                  title:
                      "${AppStrings.enterYourStoreHandle} (${AppStrings.lowerCaseOnlyAllowed})",
                  onChange: (data) {
                    widget.sellerOnBoardingBloc.onTextChangeCtrl.sink.add(true);
                  },
                ),
              ],
            ),
          );
        });
  }

  //endregion

  //region Lets Make in Bharat
  Widget makeInBharat() {
    return StreamBuilder<bool>(
        stream: widget.sellerOnBoardingBloc.storeHandleCtrl.stream,
        builder: (context, snapshot) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 21),
            child: CupertinoButton(
              borderRadius: const BorderRadius.all(Radius.circular(100)),
              color: AppColors.brandBlack,
              padding: EdgeInsets.zero,
              onPressed: () {
                widget.sellerOnBoardingBloc.addStoreApiCall();
              },
              //onPressed: () => sellerOnBoardingBloc.addWebsiteLinksApiCall(),
              //onPressed: () => sellerOnBoardingBloc.addWebsiteLinksApiCall(),
              child: Container(
                alignment: Alignment.center,
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 18.5),
                child: Text(
                    // AppStrings.makeInBharat,
                    "Create The Store",
                    style: AppTextStyle.button1Bold(
                        textColor: AppColors.appWhite)),
              ),
            ),
          );
        });
  }

//endregion

//region owned label
  Widget ownedLabel() {
    return StreamBuilder<bool>(
        stream: widget.sellerOnBoardingBloc.labelCtrl.stream,
        builder: (context, snapshot) {
          return Container(
            margin: EdgeInsets.symmetric(
                vertical: MediaQuery.of(context).size.height * 0.1),
            child: LabelSlider(
              selectedLabel: widget.sellerOnBoardingBloc.selectedLabel,
              isEnable: true,
              onChanged: (value) {
                widget.sellerOnBoardingBloc.selectedLabel = widget
                    .sellerOnBoardingBloc.ownedList
                    .firstWhere((element) => element.labelValue == value);
                //Refresh
                widget.sellerOnBoardingBloc.labelCtrl.sink.add(true);
              },
            ),
          );
        });
  }
//endregion
}
