import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/seller/add_edit_product_fields/add_edit_product_fields_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/app_tool_tip/app_tool_tip.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/seller/affiliate_promotion/affiliate_promotion_info_screen.dart';
import 'package:swadesic/features/seller/add_product/add_product_common_widgets.dart';

class AddEditProductFields extends StatefulWidget {
  const AddEditProductFields({Key? key}) : super(key: key);

  @override
  State<AddEditProductFields> createState() => _AddEditProductFieldsState();
}

class _AddEditProductFieldsState extends State<AddEditProductFields> {
  //region Bloc
  late AddEditProductFieldsBloc addEditProductFieldsBloc;
  //endregion

  //region Init
  @override
  void initState() {
    addEditProductFieldsBloc = AddEditProductFieldsBloc(context);
    addEditProductFieldsBloc.init();
    // TODO: implement initState
    super.initState();
  }

  //endregion
  //region Dispose
  @override
  void dispose() {
    addEditProductFieldsBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        brandName(),
        verticalSizedBox(30),
        productName(),
        verticalSizedBox(30),
        productSlug(),
        verticalSizedBox(30),
        productCode(),
        verticalSizedBox(30),
        productCategory(),
        verticalSizedBox(30),
        productDescription(),
        verticalSizedBox(30),
        targetGender(),
        verticalSizedBox(30),
        productPromotion(),
        verticalSizedBox(30),
        hashTags(),
        verticalSizedBox(30),
        affiliatePromotion(),
        verticalSizedBox(30),
        // // inventoryOptionsButton(),
        // verticalSizedBox(24),
      ],
    );
  }

  //region BrandName

  Widget brandName() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppTitleAndOptions(
          title: AppStrings.brandName,
          option: AppTextFields.allTextField(
            context: context,
            textEditingController: AddEditProductFieldsBloc.brandNameTextCtrl,
            hintText: AppStrings.brandNameHint,
          ),
        ),
      ],
    );
  }
  //endregion

  //region Product Name
//
  Widget productName() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppTitleAndOptions(
          title: AppStrings.productName,
          option: AppTextFields.productNameTextField(
            maxEntry: 100,
            maxLines: 2,
            minLines: 2,
            context: context,
            textEditingController: AddEditProductFieldsBloc.productNameTextCtrl,
            hintText: AppStrings.productNameHint,
          ),
        ),
      ],
    );
  }
  //endregion

  //region Product category
  Widget productCategory() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppTitleAndOptions(
          title: AppStrings.productCategoryTitle,
          option: AppTextFields.allTextField(
            maxEntry: 100,
            maxLines: 1,
            minLines: 1,
            context: context,
            textEditingController:
                AddEditProductFieldsBloc.productCategoryTextCtrl,
            hintText: AppStrings.productCategoryHint,
          ),
        ),
      ],
    );
  }
  //endregion

  //region Product Description
  //
  Widget productDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppTitleAndOptions(
          title: AppStrings.productDescription,
          option: AppTextFields.allTextField(
            textInputAction: TextInputAction.newline,
            maxEntry: 500,
            maxLines: 10,
            minLines: 10,
            context: context,
            keyboardType: TextInputType.multiline,
            textEditingController:
                AddEditProductFieldsBloc.productDescNameTextCtrl,
            hintText: AppStrings.productDescriptionHint,
          ),
        ),
      ],
    );
  }
  //endregion

  //region Product Slug
  Widget productSlug() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppTitleAndOptions(
          title: AppStrings.productSlug,
          option: AppTextFields.productSlugTextField(
            context: context,
            textEditingController: AddEditProductFieldsBloc.productSlugTextCtrl,
            hintText: AppStrings.productSlugHint,
          ),
        ),
        StreamBuilder<bool>(
          stream: addEditProductFieldsBloc.productSlugAvailabilityCtrl.stream,
          builder: (context, snapshot) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Show unavailable message
                Visibility(
                  visible: addEditProductFieldsBloc.isProductSlugAvailable != null &&
                      addEditProductFieldsBloc.isProductSlugAvailable!,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 0),
                    child: AppCommonWidgets.validAndInvalid(
                      textColor: AppColors.red,
                      buttonText: "Product slug is unavailable",
                      paddingOnTop: 0,
                    ),
                  ),
                ),
                // Show available message
                Visibility(
                  visible: addEditProductFieldsBloc.isProductSlugAvailable != null &&
                      !addEditProductFieldsBloc.isProductSlugAvailable!,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 0),
                    child: AppCommonWidgets.validAndInvalid(
                      textColor: AppColors.brandGreen,
                      buttonText: "Product slug is available",
                      paddingOnTop: 0,
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }
  //endregion

  //region Product Code
  Widget productCode() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppTitleAndOptions(
          title: "${AppStrings.productCode} ${AppStrings.optional}",
          option: AppTextFields.allTextField(
            context: context,
            textEditingController: AddEditProductFieldsBloc.productCodeTextCtrl,
            hintText: AppStrings.productCodeHint,
          ),
        ),
        StreamBuilder<bool>(
          stream: addEditProductFieldsBloc.productCodeAvailabilityCtrl.stream,
          builder: (context, snapshot) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Show unavailable message
                Visibility(
                  visible: addEditProductFieldsBloc.isProductCodeAvailable != null &&
                      addEditProductFieldsBloc.isProductCodeAvailable!,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 0),
                    child: AppCommonWidgets.validAndInvalid(
                      textColor: AppColors.red,
                      buttonText: "Product code is unavailable",
                      paddingOnTop: 0,
                    ),
                  ),
                ),
                // Show available message
                Visibility(
                  visible: addEditProductFieldsBloc.isProductCodeAvailable != null &&
                      !addEditProductFieldsBloc.isProductCodeAvailable!,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 0),
                    child: AppCommonWidgets.validAndInvalid(
                      textColor: AppColors.brandGreen,
                      buttonText: "Product code is available",
                      paddingOnTop: 0,
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }
  //endregion

  //region Target gender
  Widget targetGender() {
    return AppTitleAndOptions(
      title: AppStrings.targetGender,
      titleOption: AppToolTip(
          toolTipWidget: Text(
            "why",
            style: AppTextStyle.contentText0(
                textColor: AppColors.writingBlack1, isUnderline: true),
          ),
          message: AppStrings.targetedGenderIsForCuration),
      option: StreamBuilder<String>(
          stream: addEditProductFieldsBloc.genderCtrl.stream,
          initialData: AddEditProductFieldsBloc.gender,
          builder: (context, snapshot) {
            return Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      addEditProductFieldsBloc.onSelectGender("F");
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      decoration: BoxDecoration(
                          color: snapshot.data == "F"
                              ? AppColors.brandBlack
                              : AppColors.appWhite,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(100)),
                          border: Border.all(
                              color: snapshot.data == "F"
                                  ? AppColors.brandBlack
                                  : AppColors.darkStroke)),
                      child: Center(
                          child: Text(
                        "Female",
                        style: AppTextStyle.access0(
                            textColor: snapshot.data == "F"
                                ? AppColors.appWhite
                                : AppColors.appBlack),
                      )),
                    ),
                  ),
                ),
                horizontalSizedBox(15),
                Expanded(
                  child: InkWell(
                    onTap: () {
                      addEditProductFieldsBloc.onSelectGender("M");
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      decoration: BoxDecoration(
                          color: snapshot.data == "M"
                              ? AppColors.brandBlack
                              : AppColors.appWhite,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(100)),
                          border: Border.all(
                              color: snapshot.data == "M"
                                  ? AppColors.brandBlack
                                  : AppColors.darkStroke)),
                      child: Center(
                          child: Text(
                        "Male",
                        style: AppTextStyle.access0(
                            textColor: snapshot.data == "M"
                                ? AppColors.appWhite
                                : AppColors.appBlack),
                      )),
                    ),
                  ),
                ),
                horizontalSizedBox(15),
                Expanded(
                  child: InkWell(
                    onTap: () {
                      addEditProductFieldsBloc.onSelectGender("U");
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(100)),
                          color: snapshot.data == "U"
                              ? AppColors.brandBlack
                              : AppColors.appWhite,
                          border: Border.all(
                              color: snapshot.data == "U"
                                  ? AppColors.brandBlack
                                  : AppColors.darkStroke)),
                      child: Center(
                          child: Text(
                        "Unisex",
                        style: AppTextStyle.access0(
                            textColor: snapshot.data == "U"
                                ? AppColors.appWhite
                                : AppColors.appBlack),
                      )),
                    ),
                  ),
                )
              ],
            );
          }),
    );
  }
  //endregion

  //region Product Promotion
//
  Widget productPromotion() {
    return StreamBuilder<bool>(
        stream: addEditProductFieldsBloc.promotionLinkCtrl.stream,
        builder: (context, snapshot) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              AppTitleAndOptions(
                title: "${AppStrings.promotionLink} ${AppStrings.optional}",
                option: AppTextFields.websiteTextField(
                    context: context,
                    textEditingController:
                        AddEditProductFieldsBloc.promoLinkTextCtrl,
                    hintText: "url",
                    onChanged: () {
                      addEditProductFieldsBloc.checkUrlValidation();
                    }),
              ),

              ///If url is in-valid
              AddEditProductFieldsBloc.isUrlValid != null &&
                      !AddEditProductFieldsBloc.isUrlValid!
                  ? AppCommonWidgets.validAndInvalid(
                      buttonText: AppStrings.invalidUrl,
                      textColor: AppColors.red,
                    )
                  : const SizedBox(),

              ///If url is valid
              Visibility(
                visible: AddEditProductFieldsBloc.isUrlValid != null &&
                    AddEditProductFieldsBloc.isUrlValid!,
                child: AppCommonWidgets.validAndInvalid(
                    buttonText: AppStrings.viewTheLink,
                    textColor: AppColors.brandBlack,
                    isUnderLine: true,
                    onTap: () {
                      CommonMethods.opeAppWebView(
                          context: context,
                          webUrl:
                              AddEditProductFieldsBloc.promoLinkTextCtrl.text);
                    }),
              ),
            ],
          );
        });
  }
  //endregion

  //region In Stock

  Widget productStock() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppTitleAndOptions(
          title: AppStrings.inStock,
          option: AppTextFields.onlyNumberTextField(
            context: context,
            textEditingController: AddEditProductFieldsBloc.inStockTextCtrl,
            hintText: AppStrings.inStockHint,
          ),
        ),
        // Text( AppStrings.inStock,style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack,),),
        //
        // verticalSizedBox(10),
        // SizedBox(
        //   width: 200,
        //   child: TextFormField(
        //     keyboardType: TextInputType.number,
        //     textInputAction: TextInputAction.done,
        //     maxLines: 1,
        //     minLines: 1,
        //     controller: AddEditProductFieldsBloc.inStockTextCtrl,
        //
        //     inputFormatters: [
        //       LengthLimitingTextInputFormatter(5),
        //       FilteringTextInputFormatter.digitsOnly,
        //     ],
        //
        //     onChanged: (value) {
        //
        //     },
        //     // textCapitalization: TextCapitalization.characters,
        //     //maxLength: maxLength,
        //     style: AppTextStyle.heading2Medium(textColor: AppColors.appBlack),
        //     textCapitalization:TextCapitalization.sentences ,
        //
        //
        //     decoration: InputDecoration(
        //         filled: true,
        //         contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 11.5),
        //         fillColor: AppColors.lightestGrey,
        //         isDense: true,
        //
        //         hintText:AppStrings.inStockHint,
        //         hintStyle: AppTextStyle.heading2Regular(textColor: AppColors.writingColor3),
        //         focusedBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.lightestGrey),
        //         enabledBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.lightestGrey)),
        //   ),
        // ),
      ],
    );
  }
//endregion

  //region Hashtags
  Widget hashTags() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppTitleAndOptions(
          title: "${AppStrings.hashTags} ${AppStrings.optional}",
          titleOption: SvgPicture.asset(
            AppImages.exclamation,
            fit: BoxFit.cover,
          ),
          option: AppTextFields.allTextField(
            minLines: 5,
            maxLines: 5,
            maxEntry: 400,
            context: context,
            textEditingController: AddEditProductFieldsBloc.hashTagsTextCtrl,
            hintText: "#shoes #undergarments ",
          ),
        ),
        // SizedBox(
        //   height: 20,
        //   child: Row(
        //     mainAxisSize: MainAxisSize.max,
        //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //     children: [
        //       Text.rich(
        //         TextSpan(
        //           children: [
        //             TextSpan(text: AppStrings.hashTags,style: AppTextStyle.heading2Bold(textColor: AppColors.appBlack,),),
        //             TextSpan(
        //               text: AppStrings.optional,
        //               style: TextStyle(color: AppColors.writingColor3,
        //                   fontSize: 14,
        //                   fontFamily: "LatoSemibold",
        //                   fontWeight: FontWeight.w600
        //
        //
        //               ),
        //             ),
        //           ],
        //         ),
        //       ),
        //       SvgPicture.asset(AppImages.exclamation,fit: BoxFit.cover,),
        //     ],
        //   ),
        // ),
        //
        // verticalSizedBox(10),
        //
        //
        // TextFormField(
        //   keyboardType: TextInputType.text,
        //   textInputAction: TextInputAction.done,
        //   maxLines: 5,
        //   minLines: 5,
        //   controller:  AddEditProductFieldsBloc.hashTagsTextCtrl,
        //
        //   inputFormatters: [
        //     LengthLimitingTextInputFormatter(500),
        //   ],
        //
        //   onChanged: (value) {
        //
        //   },
        //   // textCapitalization: TextCapitalization.characters,
        //   //maxLength: maxLength,
        //   style: AppTextStyle.heading2Medium(textColor: AppColors.brandGreen),
        //   textCapitalization:TextCapitalization.sentences ,
        //
        //
        //   decoration: InputDecoration(
        //       filled: true,
        //       contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 11.5),
        //       fillColor: AppColors.lightestGrey,
        //       isDense: true,
        //
        //       hintText:"#shoes #undergarments ",
        //       hintStyle: AppTextStyle.heading2Regular(textColor: AppColors.writingColor3),
        //       focusedBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.lightestGrey),
        //       enabledBorder: AppTextFieldStyle.filledColored(fieldColors: AppColors.lightestGrey)),
        // ),
      ],
    );
  }
  //endregion

  // //region Inventory Options Button
  // Widget inventoryOptionsButton() {
  //   return AddProductCommonWidgets.deliveryReturnButton(
  //     buttonName: AppStrings.inventoryOptions,
  //     onPress: () {
  //       addEditProductFieldsBloc.goToInventoryOptionsScreen();
  //     },
  //     context: context,
  //     isDoneVisible: (AddEditProductFieldsBloc.productOptions.isNotEmpty) ||
  //         (AddEditProductFieldsBloc.productVariants.isNotEmpty),
  //   );
  // }
  //endregion

  //region MRP(Actual price)
  Widget mrp() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppTitleAndOptions(
          title: AppStrings.mrp,
          option: AppTextFields.onlyNumberTextField(
              maxEntry: 10,
              context: context,
              textEditingController: AddEditProductFieldsBloc.mrpTextCtrl,
              hintText: AppStrings.actualPrice,
              onChanged: (value) {
                addEditProductFieldsBloc.onChangeSellingPrices();
              }),
        ),
      ],
    );
  }
  //endregion

  //region Selling price
  Widget sellingPrice() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppTitleAndOptions(
          title: AppStrings.sellingPrice,
          option: AppTextFields.onlyNumberTextField(
              maxEntry: 10,
              context: context,
              textEditingController:
                  AddEditProductFieldsBloc.sellingPriceTextCtrl,
              hintText: AppStrings.sellingPrice,
              onChanged: (value) {
                addEditProductFieldsBloc.onChangeSellingPrices();
              }),
        ),
        StreamBuilder<bool>(
            stream: addEditProductFieldsBloc.productDiscountCtrl.stream,
            builder: (context, snapshot) {
              return Visibility(
                visible: addEditProductFieldsBloc.isSellingPriceWarningVisible,
                child: Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: appText(
                    AppStrings.sellingPriceShouldbe,
                    color: AppColors.red,
                    fontFamily: AppConstants.rRegular,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    maxLine: 1,
                  ),
                ),
              );
            }),
      ],
    );
  }
  //endregion

  //region Selling and discount
  Widget sellingDiscount() {
    return StreamBuilder<bool>(
        stream: addEditProductFieldsBloc.productDiscountCtrl.stream,
        builder: (context, snapshot) {
          return Visibility(
            visible: addEditProductFieldsBloc.isDiscountVisible,
            child: Container(
              padding: const EdgeInsets.only(top: 15),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "${AppStrings.oneUnitSellAt}${AddEditProductFieldsBloc.sellingPriceTextCtrl.text}",
                    style:
                        AppTextStyle.smallText(textColor: AppColors.appBlack),
                  ),
                  verticalSizedBox(5),
                  Text(
                    "${AppStrings.discount} ${addEditProductFieldsBloc.discount.toStringAsFixed(2)}%",
                    style:
                        AppTextStyle.smallText(textColor: AppColors.brandBlack),
                  )
                ],
              ),
            ),
          );
        });
  }
//endregion

  //region Affiliate Promotion
  Widget affiliatePromotion() {
    return StreamBuilder<bool>(
      stream: addEditProductFieldsBloc.affiliateCtrl.stream,
      initialData: false,
      builder: (context, snapshot) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Enable Product Promotions",
                  style: AppTextStyle.settingHeading1(
                      textColor: AppColors.appBlack),
                ),
                CupertinoSwitch(
                  value: addEditProductFieldsBloc.isAffiliatePromotionEnabled,
                  activeColor: AppColors.brandBlack,
                  onChanged: (value) {
                    addEditProductFieldsBloc.toggleAffiliate(value);
                  },
                ),
              ],
            ),
            verticalSizedBox(10),
            InkWell(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AffiliatePromotionInfoScreen(),
                  ),
                );
              },
              child: Text(
                "How Does Product Promotions Work?",
                style: AppTextStyle.contentText0(
                    textColor: AppColors.brandBlack, isUnderline: true),
              ),
            ),
            if (addEditProductFieldsBloc.isAffiliatePromotionEnabled) ...[
              verticalSizedBox(20),
              Padding(
                padding: const EdgeInsets.only(left: 16.0),
                child: AppTitleAndOptions(
                  title: "Promotion amount",
                  option: AppTextFields.onlyNumberTextField(
                    maxEntry: 10,
                    context: context,
                    textEditingController:
                        AddEditProductFieldsBloc.affiliateCommissionTextCtrl,
                    hintText: "Promotion amount",
                    onChanged: (value) {
                      addEditProductFieldsBloc.calculateAffiliateCommission();
                    },
                  ),
                ),
              ),
              if (addEditProductFieldsBloc.isCommissionWarningVisible) ...[
                verticalSizedBox(10),
                Padding(
                  padding: const EdgeInsets.only(left: 16.0),
                  child: appText(
                    AppStrings.affiliateCommissionShouldBeLessThanSellingPrice,
                    color: AppColors.red,
                    fontFamily: AppConstants.rRegular,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    maxLine: 1,
                  ),
                )
              ],
              if (addEditProductFieldsBloc.affiliateCommissionPercentage >
                  0) ...[
                verticalSizedBox(10),
                Padding(
                  padding: const EdgeInsets.only(left: 16.0),
                  child: Text(
                    "Commission in percentage: ${addEditProductFieldsBloc.affiliateCommissionPercentage.toStringAsFixed(2)}%",
                    style:
                        AppTextStyle.smallText(textColor: AppColors.brandBlack),
                  ),
                ),
              ],
            ],
          ],
        );
      },
    );
  }
  //endregion
}
